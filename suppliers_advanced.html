<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الموردين والطبليات - السوق المركزي</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .content {
            padding: 30px;
        }

        .tabs {
            display: flex;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 20px;
            overflow: hidden;
        }

        .tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            background: #e9ecef;
            cursor: pointer;
            border: none;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s;
        }

        .tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }

        .stat-card h3 {
            font-size: 2em;
            margin-bottom: 5px;
        }

        .form-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 30px;
        }

        .form-section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5em;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: transform 0.2s;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        }

        .btn-edit {
            background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
            color: #333;
        }

        .file-upload {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: border-color 0.3s;
        }

        .file-upload:hover {
            border-color: #667eea;
        }

        .file-upload.dragover {
            border-color: #667eea;
            background-color: #f0f8ff;
        }

        /* تنسيق الطبليات */
        .tables-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .table-card {
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 15px;
            background: white;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .table-card:hover {
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            transform: translateY(-2px);
        }

        .table-card.occupied {
            border-color: #28a745;
            background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
        }

        .table-card.available {
            border-color: #ffc107;
            background: linear-gradient(135deg, #fffef8 0%, #fef9e7 100%);
        }

        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .table-number {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }

        .table-status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-occupied {
            background: #28a745;
            color: white;
        }

        .status-available {
            background: #ffc107;
            color: #333;
        }

        .table-info p {
            margin: 8px 0;
            font-size: 14px;
        }

        .revenue-info {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            text-align: center;
        }

        .revenue-info p {
            margin: 0;
            font-weight: bold;
            color: #28a745;
        }

        /* تنسيق التقارير */
        .report-section {
            margin: 30px 0;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .report-section h3 {
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #007bff;
        }

        .report-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .report-table th,
        .report-table td {
            padding: 12px;
            text-align: center;
            border: 1px solid #ddd;
        }

        .report-table th {
            background: #007bff;
            color: white;
            font-weight: bold;
        }

        .report-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        .report-table tr:hover {
            background: #e3f2fd;
        }

        .uploaded-files {
            margin-top: 10px;
        }

        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 4px;
            margin-bottom: 5px;
        }

        .file-item a {
            color: #667eea;
            text-decoration: none;
        }

        .file-item button {
            background: #dc3545;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 3px;
            cursor: pointer;
        }

        .suppliers-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .suppliers-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            text-align: right;
            font-weight: bold;
        }

        .suppliers-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }

        .suppliers-table tr:hover {
            background-color: #f8f9fa;
        }

        .actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .search-box {
            width: 100%;
            padding: 12px;
            margin-bottom: 20px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }

        .tables-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .table-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            border: 2px solid #e9ecef;
        }

        .table-card.occupied {
            border-color: #28a745;
            background: #f8fff9;
        }

        .table-card.available {
            border-color: #6c757d;
            background: #f8f9fa;
        }

        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .table-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #333;
        }

        .table-status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-occupied {
            background: #28a745;
            color: white;
        }

        .status-available {
            background: #6c757d;
            color: white;
        }

        .table-info {
            margin-bottom: 15px;
        }

        .table-info p {
            margin-bottom: 5px;
            color: #666;
        }

        .revenue-info {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }

        .revenue-info strong {
            color: #1976d2;
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }

            .actions {
                flex-direction: column;
            }

            .suppliers-table {
                font-size: 12px;
            }

            .tabs {
                flex-direction: column;
            }

            .tables-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🏢 نظام إدارة الموردين والطبليات</h1>
            <p>السوق المركزي - إدارة شاملة للموردين والطبليات والعقود</p>
        </div>

        <div class="content">
            <!-- التبويبات -->
            <div class="tabs">
                <button class="tab active" onclick="showTab('suppliers')">إدارة الموردين</button>
                <button class="tab" onclick="showTab('contracts')">إدارة العقود</button>
                <button class="tab" onclick="showTab('tables')">إدارة الطبليات</button>
                <button class="tab" onclick="showTab('reports')">التقارير والإحصائيات</button>
            </div>

            <!-- تبويب الموردين -->
            <div id="suppliers-tab" class="tab-content active">
                <!-- إحصائيات الموردين -->
                <div class="stats">
                    <div class="stat-card">
                        <h3 id="totalSuppliers">0</h3>
                        <p>إجمالي الموردين</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="activeSuppliers">0</h3>
                        <p>الموردين النشطين</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="rentSuppliers">0</h3>
                        <p>موردين بالإيجار</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="percentageSuppliers">0</h3>
                        <p>موردين بالنسبة</p>
                    </div>
                </div>

                <!-- نموذج إضافة مورد جديد -->
                <div class="form-section">
                    <h2>إضافة مورد جديد</h2>
                    <form id="supplierForm">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="supplierName">اسم المورد *</label>
                                <input type="text" id="supplierName" required>
                            </div>
                            <div class="form-group">
                                <label for="companyName">اسم الشركة</label>
                                <input type="text" id="companyName">
                            </div>
                            <div class="form-group">
                                <label for="phone">رقم الهاتف *</label>
                                <input type="tel" id="phone" required>
                            </div>
                            <div class="form-group">
                                <label for="email">البريد الإلكتروني</label>
                                <input type="email" id="email">
                            </div>
                            <div class="form-group">
                                <label for="category">الفئة</label>
                                <select id="category">
                                    <option value="">اختر الفئة</option>
                                    <option value="أكياس قمامة">أكياس قمامة</option>
                                    <option value="مفارش سفرة">مفارش سفرة</option>
                                    <option value="أصناف استهلاكية">أصناف استهلاكية</option>
                                    <option value="أخرى">أخرى</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="supplierType">نوع المورد *</label>
                                <select id="supplierType" required onchange="toggleSupplierTypeFields()">
                                    <option value="">اختر نوع المورد</option>
                                    <option value="إيجار">إيجار شهري</option>
                                    <option value="نسبة خصم">نسبة خصم</option>
                                    <option value="نسبة مجاني">نسبة مجاني</option>
                                </select>
                            </div>
                            <div class="form-group" id="rentGroup" style="display:none;">
                                <label for="monthlyRent">الإيجار الشهري (دينار)</label>
                                <input type="number" id="monthlyRent" min="0" step="0.01" placeholder="60">
                            </div>
                            <div class="form-group" id="discountGroup" style="display:none;">
                                <label for="discountPercentage">نسبة الخصم (%)</label>
                                <input type="number" id="discountPercentage" min="0" max="100" step="0.1">
                            </div>
                            <div class="form-group" id="freeGroup" style="display:none;">
                                <label for="freePercentage">نسبة المجاني (%)</label>
                                <input type="number" id="freePercentage" min="0" max="100" step="0.1">
                            </div>
                            <div class="form-group">
                                <label for="status">الحالة</label>
                                <select id="status">
                                    <option value="نشط">نشط</option>
                                    <option value="غير نشط">غير نشط</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="address">العنوان</label>
                            <textarea id="address" rows="3"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="notes">ملاحظات</label>
                            <textarea id="notes" rows="3"></textarea>
                        </div>

                        <!-- معلومات العقد -->
                        <div class="form-group">
                            <label for="contractNumber">رقم العقد</label>
                            <input type="text" id="contractNumber" placeholder="مثال: عقد-2024-001">
                        </div>

                        <div class="form-group">
                            <label for="contractDate">تاريخ العقد</label>
                            <input type="date" id="contractDate">
                        </div>

                        <div class="form-group">
                            <label for="contractValue">قيمة العقد (دينار)</label>
                            <input type="number" id="contractValue" min="0" step="0.01" placeholder="0.00">
                        </div>

                        <div class="form-group">
                            <label for="contractDuration">مدة العقد (شهر)</label>
                            <input type="number" id="contractDuration" min="1" placeholder="12">
                        </div>

                        <div class="form-group">
                            <label for="contractStatus">حالة العقد</label>
                            <select id="contractStatus">
                                <option value="نشط">نشط</option>
                                <option value="منتهي">منتهي</option>
                                <option value="معلق">معلق</option>
                                <option value="ملغي">ملغي</option>
                            </select>
                        </div>

                        <!-- رفع العقود -->
                        <div class="form-group">
                            <label>العقود (PDF)</label>
                            <div class="file-upload" id="fileUpload">
                                <p>📄 اسحب ملفات PDF هنا أو انقر للاختيار</p>
                                <input type="file" id="contractFiles" multiple accept=".pdf" style="display:none;">
                            </div>
                            <div class="uploaded-files" id="uploadedFiles"></div>
                        </div>

                        <button type="submit" class="btn">إضافة المورد</button>
                        <button type="button" class="btn btn-edit" id="updateBtn" style="display:none;">تحديث
                            المورد</button>
                        <button type="button" class="btn" id="cancelBtn" style="display:none;">إلغاء</button>
                    </form>

                    <!-- استيراد ملفات Excel -->
                    <div class="form-section" style="margin-top: 30px;">
                        <h3>استيراد البيانات</h3>
                        <div class="form-grid">
                            <button type="button" class="btn" onclick="importSuppliersExcel()">استيراد ملف الموردين
                                (Excel)</button>
                            <button type="button" class="btn" onclick="importContractsExcel()">استيراد ملف العقود
                                (Excel)</button>
                            <button type="button" class="btn" onclick="exportData()">تصدير البيانات (JSON)</button>
                            <button type="button" class="btn" onclick="importData()">استيراد البيانات (JSON)</button>
                        </div>
                    </div>
                    </form>
                </div>

                <!-- قائمة الموردين -->
                <div class="form-section">
                    <h2>قائمة الموردين</h2>
                    <input type="text" class="search-box" id="searchBox" placeholder="البحث في الموردين...">
                    <div style="overflow-x: auto;">
                        <table class="suppliers-table" id="suppliersTable">
                            <thead>
                                <tr>
                                    <th>الإجراءات</th>
                                    <th>العقود</th>
                                    <th>رقم العقد</th>
                                    <th>حالة العقد</th>
                                    <th>قيمة العقد</th>
                                    <th>القيمة المالية</th>
                                    <th>نوع المورد</th>
                                    <th>الحالة</th>
                                    <th>الفئة</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الهاتف</th>
                                    <th>الشركة</th>
                                    <th>اسم المورد</th>
                                    <th>#</th>
                                </tr>
                            </thead>
                            <tbody id="suppliersTableBody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- تبويب العقود -->
            <div id="contracts-tab" class="tab-content">
                <h2>إدارة العقود</h2>

                <!-- إحصائيات العقود -->
                <div class="stats">
                    <div class="stat-card">
                        <h3 id="activeContractsCount">0</h3>
                        <p>العقود النشطة</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="expiredContractsCount">0</h3>
                        <p>العقود المنتهية</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="totalContractValue">0</h3>
                        <p>إجمالي قيمة العقود (دينار)</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="avgContractDuration">0</h3>
                        <p>متوسط مدة العقد (شهر)</p>
                    </div>
                </div>

                <!-- جدول العقود -->
                <div class="table-section">
                    <h3>قائمة العقود</h3>
                    <div style="overflow-x: auto;">
                        <table class="suppliers-table" id="contractsTable">
                            <thead>
                                <tr>
                                    <th>الإجراءات</th>
                                    <th>حالة العقد</th>
                                    <th>مدة العقد</th>
                                    <th>قيمة العقد</th>
                                    <th>تاريخ العقد</th>
                                    <th>رقم العقد</th>
                                    <th>اسم الشركة</th>
                                    <th>اسم المورد</th>
                                    <th>#</th>
                                </tr>
                            </thead>
                            <tbody id="contractsTableBody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- تبويب الطبليات -->
            <div id="tables-tab" class="tab-content">
                <!-- إحصائيات الطبليات -->
                <div class="stats">
                    <div class="stat-card">
                        <h3 id="totalTables">0</h3>
                        <p>إجمالي الطبليات</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="occupiedTables">0</h3>
                        <p>طبليات مؤجرة</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="availableTables">0</h3>
                        <p>طبليات متاحة</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="monthlyRevenue">0</h3>
                        <p>الإيراد الشهري (دينار)</p>
                    </div>
                </div>

                <!-- نموذج إضافة طبلية جديدة -->
                <div class="form-section">
                    <h2>إضافة طبلية جديدة</h2>
                    <form id="tableForm">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="tableNumber">رقم الطبلية *</label>
                                <input type="text" id="tableNumber" required>
                            </div>
                            <div class="form-group">
                                <label for="tableLocation">الموقع</label>
                                <input type="text" id="tableLocation" placeholder="مثال: الممر الأول">
                            </div>
                            <div class="form-group">
                                <label for="tableSize">حجم الطبلية</label>
                                <select id="tableSize">
                                    <option value="4 عيون">4 عيون (60 سم)</option>
                                    <option value="6 عيون">6 عيون (90 سم)</option>
                                    <option value="8 عيون">8 عيون (120 سم)</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="monthlyRentTable">الإيجار الشهري (دينار)</label>
                                <input type="number" id="monthlyRentTable" min="0" step="0.01" value="60">
                            </div>
                            <div class="form-group">
                                <label for="tableStatus">حالة الطبلية</label>
                                <select id="tableStatus">
                                    <option value="متاحة">متاحة</option>
                                    <option value="مؤجرة">مؤجرة</option>
                                    <option value="صيانة">صيانة</option>
                                </select>
                            </div>
                            <div class="form-group" id="supplierSelectGroup" style="display:none;">
                                <label for="assignedSupplier">المورد المستأجر</label>
                                <select id="assignedSupplier">
                                    <option value="">اختر المورد</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="tableNotes">ملاحظات</label>
                            <textarea id="tableNotes" rows="3"></textarea>
                        </div>

                        <button type="submit" class="btn">إضافة الطبلية</button>
                        <button type="button" class="btn btn-edit" id="updateTableBtn" style="display:none;">تحديث
                            الطبلية</button>
                        <button type="button" class="btn" id="cancelTableBtn" style="display:none;">إلغاء</button>
                    </form>
                </div>

                <!-- عرض الطبليات -->
                <div class="form-section">
                    <h2>إدارة الطبليات</h2>
                    <input type="text" class="search-box" id="searchTables" placeholder="البحث في الطبليات...">

                    <div class="tables-grid" id="tablesGrid">
                        <!-- سيتم إضافة الطبليات هنا ديناميكياً -->
                    </div>
                </div>
            </div>

            <!-- تبويب التقارير -->
            <div id="reports-tab" class="tab-content">
                <div class="stats">
                    <div class="stat-card">
                        <h3 id="totalRevenue">0</h3>
                        <p>إجمالي الإيرادات الشهرية</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="yearlyRevenue">0</h3>
                        <p>الإيرادات السنوية المتوقعة</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="occupancyRate">0%</h3>
                        <p>معدل الإشغال</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="avgRentPerTable">0</h3>
                        <p>متوسط الإيجار للطبلية</p>
                    </div>
                </div>

                <div class="form-section">
                    <h2>تقرير مفصل</h2>
                    <div id="detailedReport">
                        <!-- سيتم إضافة التقرير المفصل هنا -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نوافذ منبثقة -->
    <div id="detailsModal" class="modal" style="display:none;">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h2>تفاصيل المورد</h2>
            <div id="supplierDetails"></div>
        </div>
    </div>

    <script>
        // بيانات التطبيق
        let suppliers = JSON.parse(localStorage.getItem('suppliers')) || [];
        let tables = JSON.parse(localStorage.getItem('tables')) || [];
        let editingSupplierIndex = -1;
        let editingTableIndex = -1;

        // عناصر DOM
        const supplierForm = document.getElementById('supplierForm');
        const tableForm = document.getElementById('tableForm');
        const suppliersTableBody = document.getElementById('suppliersTableBody');
        const tablesGrid = document.getElementById('tablesGrid');
        const searchBox = document.getElementById('searchBox');
        const searchTables = document.getElementById('searchTables');

        // تحميل البيانات عند بدء التشغيل
        document.addEventListener('DOMContentLoaded', function () {
            // إضافة بيانات تجريبية إذا لم تكن موجودة
            if (suppliers.length === 0) {
                addSampleData();
            }
            displaySuppliers();
            displayTables();
            updateAllStats();
            setupFileUpload();
            populateSupplierSelect();
        });

        // إضافة بيانات تجريبية
        function addSampleData() {
            const sampleSuppliers = [
                {
                    id: Date.now() + 1,
                    name: 'أحمد محمد علي',
                    company: 'شركة النور للأصناف الاستهلاكية',
                    phone: '0912345678',
                    email: '<EMAIL>',
                    category: 'أكياس قمامة',
                    supplierType: 'إيجار',
                    monthlyRent: 150,
                    discountPercentage: 0,
                    freePercentage: 0,
                    status: 'نشط',
                    address: 'طرابلس - شارع الجمهورية',
                    notes: 'مورد موثوق ومنتظم في التوريد',
                    contractNumber: 'عقد-2024-001',
                    contractDate: '2024-01-15',
                    contractValue: 1800,
                    contractDuration: 12,
                    contractStatus: 'نشط',
                    contracts: [],
                    dateAdded: new Date().toLocaleDateString('ar-EG')
                },
                {
                    id: Date.now() + 2,
                    name: 'فاطمة سالم',
                    company: 'مؤسسة الزهراء للمفارش',
                    phone: '0923456789',
                    email: '<EMAIL>',
                    category: 'مفارش سفرة',
                    supplierType: 'نسبة خصم',
                    monthlyRent: 0,
                    discountPercentage: 15,
                    freePercentage: 0,
                    status: 'نشط',
                    address: 'بنغازي - شارع عمر المختار',
                    notes: 'تخصص في المفارش عالية الجودة',
                    contractNumber: 'عقد-2024-002',
                    contractDate: '2024-02-01',
                    contractValue: 2500,
                    contractDuration: 6,
                    contractStatus: 'نشط',
                    contracts: [],
                    dateAdded: new Date().toLocaleDateString('ar-EG')
                },
                {
                    id: Date.now() + 3,
                    name: 'محمد الطاهر',
                    company: 'شركة البركة التجارية',
                    phone: '0934567890',
                    email: '<EMAIL>',
                    category: 'الأصناف الاستهلاكية',
                    supplierType: 'نسبة مجاني',
                    monthlyRent: 0,
                    discountPercentage: 0,
                    freePercentage: 10,
                    status: 'نشط',
                    address: 'مصراتة - السوق المركزي',
                    notes: 'يوفر تشكيلة واسعة من المنتجات',
                    contractNumber: 'عقد-2024-003',
                    contractDate: '2024-01-20',
                    contractValue: 3000,
                    contractDuration: 12,
                    contractStatus: 'نشط',
                    contracts: [],
                    dateAdded: new Date().toLocaleDateString('ar-EG')
                }
            ];

            const sampleTables = [
                {
                    id: Date.now() + 10,
                    number: 'T-001',
                    location: 'الممر الأول',
                    size: '4 عيون',
                    monthlyRent: 60,
                    status: 'مؤجرة',
                    assignedSupplier: Date.now() + 1,
                    notes: 'طبلية في موقع ممتاز',
                    dateAdded: new Date().toLocaleDateString('ar-EG')
                },
                {
                    id: Date.now() + 11,
                    number: 'T-002',
                    location: 'الممر الأول',
                    size: '4 عيون',
                    monthlyRent: 60,
                    status: 'مؤجرة',
                    assignedSupplier: Date.now() + 2,
                    notes: 'طبلية بجانب المدخل الرئيسي',
                    dateAdded: new Date().toLocaleDateString('ar-EG')
                },
                {
                    id: Date.now() + 12,
                    number: 'T-003',
                    location: 'الممر الثاني',
                    size: '4 عيون',
                    monthlyRent: 60,
                    status: 'متاحة',
                    assignedSupplier: '',
                    notes: 'طبلية متاحة للإيجار',
                    dateAdded: new Date().toLocaleDateString('ar-EG')
                }
            ];

            suppliers = sampleSuppliers;
            tables = sampleTables;
            localStorage.setItem('suppliers', JSON.stringify(suppliers));
            localStorage.setItem('tables', JSON.stringify(tables));
        }

        // إدارة التبويبات
        function showTab(tabName) {
            // إخفاء جميع التبويبات
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // إظهار التبويب المحدد
            document.getElementById(tabName + '-tab').classList.add('active');
            event.target.classList.add('active');

            // تحديث البيانات حسب التبويب
            if (tabName === 'suppliers') {
                displaySuppliers();
                updateSupplierStats();
            } else if (tabName === 'contracts') {
                displayContracts();
                updateContractStats();
            } else if (tabName === 'tables') {
                displayTables();
                updateTableStats();
            } else if (tabName === 'reports') {
                generateReports();
            }
        }

        // عرض العقود
        function displayContracts() {
            const contractsTableBody = document.getElementById('contractsTableBody');
            contractsTableBody.innerHTML = '';

            suppliers.forEach((supplier, index) => {
                if (supplier.contractNumber) {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>
                            <div class="actions">
                                <button class="btn btn-edit" onclick="editSupplier(${index})">تعديل</button>
                                <button class="btn" onclick="viewSupplierDetails(${index})">عرض المورد</button>
                            </div>
                        </td>
                        <td><span style="color: ${getContractStatusColor(supplier.contractStatus)}">${supplier.contractStatus || 'غير محدد'}</span></td>
                        <td>${supplier.contractDuration ? supplier.contractDuration + ' شهر' : 'غير محدد'}</td>
                        <td><strong>${supplier.contractValue ? supplier.contractValue + ' دينار' : 'غير محدد'}</strong></td>
                        <td>${supplier.contractDate || 'غير محدد'}</td>
                        <td><strong>${supplier.contractNumber}</strong></td>
                        <td>${supplier.company}</td>
                        <td>${supplier.name}</td>
                        <td>${index + 1}</td>
                    `;
                    contractsTableBody.appendChild(row);
                }
            });
        }

        // تحديث إحصائيات العقود
        function updateContractStats() {
            const contractsWithData = suppliers.filter(s => s.contractNumber);
            const activeContracts = contractsWithData.filter(s => s.contractStatus === 'نشط');
            const expiredContracts = contractsWithData.filter(s => s.contractStatus === 'منتهي');
            const totalValue = contractsWithData.reduce((sum, s) => sum + (s.contractValue || 0), 0);
            const avgDuration = contractsWithData.length > 0 ?
                contractsWithData.reduce((sum, s) => sum + (s.contractDuration || 0), 0) / contractsWithData.length : 0;

            document.getElementById('activeContractsCount').textContent = activeContracts.length;
            document.getElementById('expiredContractsCount').textContent = expiredContracts.length;
            document.getElementById('totalContractValue').textContent = totalValue.toLocaleString();
            document.getElementById('avgContractDuration').textContent = Math.round(avgDuration);
        }

        // إدارة نوع المورد
        function toggleSupplierTypeFields() {
            const supplierType = document.getElementById('supplierType').value;
            const rentGroup = document.getElementById('rentGroup');
            const discountGroup = document.getElementById('discountGroup');
            const freeGroup = document.getElementById('freeGroup');

            // إخفاء جميع المجموعات
            rentGroup.style.display = 'none';
            discountGroup.style.display = 'none';
            freeGroup.style.display = 'none';

            // إظهار المجموعة المناسبة
            if (supplierType === 'إيجار') {
                rentGroup.style.display = 'block';
            } else if (supplierType === 'نسبة خصم') {
                discountGroup.style.display = 'block';
            } else if (supplierType === 'نسبة مجاني') {
                freeGroup.style.display = 'block';
            }
        }

        // إدارة حالة الطبلية
        document.getElementById('tableStatus').addEventListener('change', function () {
            const supplierSelectGroup = document.getElementById('supplierSelectGroup');
            if (this.value === 'مؤجرة') {
                supplierSelectGroup.style.display = 'block';
            } else {
                supplierSelectGroup.style.display = 'none';
            }
        });

        // إضافة مورد جديد
        supplierForm.addEventListener('submit', function (e) {
            e.preventDefault();

            const supplier = {
                id: editingSupplierIndex === -1 ? Date.now() : suppliers[editingSupplierIndex].id,
                name: document.getElementById('supplierName').value,
                company: document.getElementById('companyName').value,
                phone: document.getElementById('phone').value,
                email: document.getElementById('email').value,
                category: document.getElementById('category').value,
                supplierType: document.getElementById('supplierType').value,
                monthlyRent: parseFloat(document.getElementById('monthlyRent').value) || 0,
                discountPercentage: parseFloat(document.getElementById('discountPercentage').value) || 0,
                freePercentage: parseFloat(document.getElementById('freePercentage').value) || 0,
                status: document.getElementById('status').value,
                address: document.getElementById('address').value,
                notes: document.getElementById('notes').value,
                // معلومات العقد
                contractNumber: document.getElementById('contractNumber').value,
                contractDate: document.getElementById('contractDate').value,
                contractValue: parseFloat(document.getElementById('contractValue').value) || 0,
                contractDuration: parseInt(document.getElementById('contractDuration').value) || 0,
                contractStatus: document.getElementById('contractStatus').value,
                contracts: editingSupplierIndex === -1 ? [] : suppliers[editingSupplierIndex].contracts || [],
                dateAdded: editingSupplierIndex === -1 ? new Date().toLocaleDateString('ar-EG') : suppliers[editingSupplierIndex].dateAdded
            };

            if (editingSupplierIndex === -1) {
                suppliers.push(supplier);
            } else {
                suppliers[editingSupplierIndex] = supplier;
                editingSupplierIndex = -1;
                document.getElementById('updateBtn').style.display = 'none';
                document.getElementById('cancelBtn').style.display = 'none';
                document.querySelector('.btn[type="submit"]').style.display = 'inline-block';
            }

            localStorage.setItem('suppliers', JSON.stringify(suppliers));
            supplierForm.reset();
            displaySuppliers();
            updateAllStats();
            populateSupplierSelect();
            alert('تم حفظ بيانات المورد بنجاح!');
        });

        // إضافة طبلية جديدة
        tableForm.addEventListener('submit', function (e) {
            e.preventDefault();

            const table = {
                id: editingTableIndex === -1 ? Date.now() : tables[editingTableIndex].id,
                number: document.getElementById('tableNumber').value,
                location: document.getElementById('tableLocation').value,
                size: document.getElementById('tableSize').value,
                monthlyRent: parseFloat(document.getElementById('monthlyRentTable').value) || 60,
                status: document.getElementById('tableStatus').value,
                assignedSupplier: document.getElementById('assignedSupplier').value,
                notes: document.getElementById('tableNotes').value,
                dateAdded: editingTableIndex === -1 ? new Date().toLocaleDateString('ar-EG') : tables[editingTableIndex].dateAdded
            };

            if (editingTableIndex === -1) {
                tables.push(table);
            } else {
                tables[editingTableIndex] = table;
                editingTableIndex = -1;
                document.getElementById('updateTableBtn').style.display = 'none';
                document.getElementById('cancelTableBtn').style.display = 'none';
                document.querySelector('#tableForm .btn[type="submit"]').style.display = 'inline-block';
            }

            localStorage.setItem('tables', JSON.stringify(tables));
            tableForm.reset();
            displayTables();
            updateAllStats();
            alert('تم حفظ بيانات الطبلية بنجاح!');
        });

        // عرض الموردين
        function displaySuppliers(filteredSuppliers = suppliers) {
            suppliersTableBody.innerHTML = '';

            filteredSuppliers.forEach((supplier, index) => {
                const originalIndex = suppliers.indexOf(supplier);
                const financialValue = getSupplierFinancialValue(supplier);
                const contractsCount = supplier.contracts ? supplier.contracts.length : 0;

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <div class="actions">
                            <button class="btn btn-edit" onclick="editSupplier(${originalIndex})">تعديل</button>
                            <button class="btn btn-danger" onclick="deleteSupplier(${originalIndex})">حذف</button>
                            <button class="btn" onclick="viewSupplierDetails(${originalIndex})">عرض</button>
                        </div>
                    </td>
                    <td>
                        <span style="color: ${contractsCount > 0 ? 'green' : 'orange'}">
                            ${contractsCount} عقد
                        </span>
                    </td>
                    <td>${supplier.contractNumber || 'غير محدد'}</td>
                    <td><span style="color: ${getContractStatusColor(supplier.contractStatus)}">${supplier.contractStatus || 'غير محدد'}</span></td>
                    <td><strong>${supplier.contractValue ? supplier.contractValue + ' دينار' : 'غير محدد'}</strong></td>
                    <td><strong>${financialValue}</strong></td>
                    <td><span style="background: ${getSupplierTypeColor(supplier.supplierType)}; color: white; padding: 3px 8px; border-radius: 10px; font-size: 12px;">${supplier.supplierType}</span></td>
                    <td><span style="color: ${supplier.status === 'نشط' ? 'green' : 'red'}">${supplier.status}</span></td>
                    <td>${supplier.category}</td>
                    <td>${supplier.email}</td>
                    <td>${supplier.phone}</td>
                    <td>${supplier.company}</td>
                    <td><strong>${supplier.name}</strong></td>
                    <td>${originalIndex + 1}</td>
                `;
                suppliersTableBody.appendChild(row);
            });
        }

        // عرض الطبليات
        function displayTables(filteredTables = tables) {
            tablesGrid.innerHTML = '';

            filteredTables.forEach((table, index) => {
                const originalIndex = tables.indexOf(table);
                const supplierName = table.assignedSupplier ? getSupplierNameById(table.assignedSupplier) : '';
                const yearlyRevenue = table.monthlyRent * 12;

                const tableCard = document.createElement('div');
                tableCard.className = `table-card ${table.status === 'مؤجرة' ? 'occupied' : 'available'}`;
                tableCard.innerHTML = `
                    <div class="table-header">
                        <div class="table-number">طبلية ${table.number}</div>
                        <div class="table-status ${table.status === 'مؤجرة' ? 'status-occupied' : 'status-available'}">
                            ${table.status}
                        </div>
                    </div>
                    <div class="table-info">
                        <p><strong>الموقع:</strong> ${table.location}</p>
                        <p><strong>الحجم:</strong> ${table.size}</p>
                        <p><strong>الإيجار الشهري:</strong> ${table.monthlyRent} دينار</p>
                        ${table.assignedSupplier ? `<p><strong>المورد:</strong> ${supplierName}</p>` : ''}
                        ${table.notes ? `<p><strong>ملاحظات:</strong> ${table.notes}</p>` : ''}
                    </div>
                    <div class="revenue-info">
                        <p><strong>الإيراد السنوي:</strong> ${yearlyRevenue} دينار</p>
                    </div>
                    <div class="actions" style="margin-top: 15px;">
                        <button class="btn btn-edit" onclick="editTable(${originalIndex})">تعديل</button>
                        <button class="btn btn-danger" onclick="deleteTable(${originalIndex})">حذف</button>
                        ${table.status === 'متاحة' ? `<button class="btn" onclick="assignTable(${originalIndex})">تأجير</button>` : ''}
                        ${table.status === 'مؤجرة' ? `<button class="btn" onclick="releaseTable(${originalIndex})">إخلاء</button>` : ''}
                    </div>
                `;
                tablesGrid.appendChild(tableCard);
            });
        }

        // الحصول على القيمة المالية للمورد
        function getSupplierFinancialValue(supplier) {
            if (supplier.supplierType === 'إيجار') {
                return `${supplier.monthlyRent} دينار/شهر`;
            } else if (supplier.supplierType === 'نسبة خصم') {
                return `${supplier.discountPercentage}% خصم`;
            } else if (supplier.supplierType === 'نسبة مجاني') {
                return `${supplier.freePercentage}% مجاني`;
            }
            return 'غير محدد';
        }

        // الحصول على لون نوع المورد
        function getSupplierTypeColor(type) {
            switch (type) {
                case 'إيجار': return '#28a745';
                case 'نسبة خصم': return '#ffc107';
                case 'نسبة مجاني': return '#17a2b8';
                default: return '#6c757d';
            }
        }

        // الحصول على لون حالة العقد
        function getContractStatusColor(status) {
            switch (status) {
                case 'نشط': return '#28a745';
                case 'منتهي': return '#dc3545';
                case 'معلق': return '#ffc107';
                case 'ملغي': return '#6c757d';
                default: return '#6c757d';
            }
        }

        // الحصول على اسم المورد بالمعرف
        function getSupplierNameById(supplierId) {
            const supplier = suppliers.find(s => s.id == supplierId);
            return supplier ? supplier.name : 'غير محدد';
        }

        // تعديل مورد
        function editSupplier(index) {
            const supplier = suppliers[index];
            editingSupplierIndex = index;

            document.getElementById('supplierName').value = supplier.name;
            document.getElementById('companyName').value = supplier.company;
            document.getElementById('phone').value = supplier.phone;
            document.getElementById('email').value = supplier.email;
            document.getElementById('category').value = supplier.category;
            document.getElementById('supplierType').value = supplier.supplierType;
            document.getElementById('monthlyRent').value = supplier.monthlyRent || '';
            document.getElementById('discountPercentage').value = supplier.discountPercentage || '';
            document.getElementById('freePercentage').value = supplier.freePercentage || '';
            document.getElementById('status').value = supplier.status;
            document.getElementById('address').value = supplier.address;
            document.getElementById('notes').value = supplier.notes;

            // حقول العقد
            document.getElementById('contractNumber').value = supplier.contractNumber || '';
            document.getElementById('contractDate').value = supplier.contractDate || '';
            document.getElementById('contractValue').value = supplier.contractValue || '';
            document.getElementById('contractDuration').value = supplier.contractDuration || '';
            document.getElementById('contractStatus').value = supplier.contractStatus || 'نشط';

            toggleSupplierTypeFields();

            document.getElementById('updateBtn').style.display = 'inline-block';
            document.getElementById('cancelBtn').style.display = 'inline-block';
            document.querySelector('.btn[type="submit"]').style.display = 'none';

            document.getElementById('supplierName').scrollIntoView();
        }

        // تعديل طبلية
        function editTable(index) {
            const table = tables[index];
            editingTableIndex = index;

            document.getElementById('tableNumber').value = table.number;
            document.getElementById('tableLocation').value = table.location;
            document.getElementById('tableSize').value = table.size;
            document.getElementById('monthlyRentTable').value = table.monthlyRent;
            document.getElementById('tableStatus').value = table.status;
            document.getElementById('assignedSupplier').value = table.assignedSupplier || '';
            document.getElementById('tableNotes').value = table.notes;

            // إظهار/إخفاء قائمة الموردين
            const supplierSelectGroup = document.getElementById('supplierSelectGroup');
            supplierSelectGroup.style.display = table.status === 'مؤجرة' ? 'block' : 'none';

            document.getElementById('updateTableBtn').style.display = 'inline-block';
            document.getElementById('cancelTableBtn').style.display = 'inline-block';
            document.querySelector('#tableForm .btn[type="submit"]').style.display = 'none';

            document.getElementById('tableNumber').scrollIntoView();
        }

        // حذف مورد
        function deleteSupplier(index) {
            if (confirm('هل أنت متأكد من حذف هذا المورد؟')) {
                suppliers.splice(index, 1);
                localStorage.setItem('suppliers', JSON.stringify(suppliers));
                displaySuppliers();
                updateAllStats();
                populateSupplierSelect();
                alert('تم حذف المورد بنجاح!');
            }
        }

        // حذف طبلية
        function deleteTable(index) {
            if (confirm('هل أنت متأكد من حذف هذه الطبلية؟')) {
                tables.splice(index, 1);
                localStorage.setItem('tables', JSON.stringify(tables));
                displayTables();
                updateAllStats();
                alert('تم حذف الطبلية بنجاح!');
            }
        }

        // عرض تفاصيل المورد
        function viewSupplierDetails(index) {
            const supplier = suppliers[index];
            const detailsDiv = document.getElementById('supplierDetails');

            const contractsList = supplier.contracts && supplier.contracts.length > 0
                ? supplier.contracts.map(contract => `<a href="${contract.url}" target="_blank">${contract.name}</a>`).join('<br>')
                : 'لا توجد عقود مرفوعة';

            detailsDiv.innerHTML = `
                <div style="line-height: 2;">
                    <h3 style="color: #007bff; border-bottom: 2px solid #007bff; padding-bottom: 10px;">معلومات المورد</h3>
                    <p><strong>اسم المورد:</strong> ${supplier.name}</p>
                    <p><strong>اسم الشركة:</strong> ${supplier.company}</p>
                    <p><strong>رقم الهاتف:</strong> ${supplier.phone}</p>
                    <p><strong>البريد الإلكتروني:</strong> ${supplier.email}</p>
                    <p><strong>الفئة:</strong> ${supplier.category}</p>
                    <p><strong>نوع المورد:</strong> ${supplier.supplierType}</p>
                    <p><strong>القيمة المالية:</strong> ${getSupplierFinancialValue(supplier)}</p>
                    <p><strong>الحالة:</strong> <span style="color: ${supplier.status === 'نشط' ? 'green' : 'red'}">${supplier.status}</span></p>
                    <p><strong>العنوان:</strong> ${supplier.address}</p>
                    <p><strong>ملاحظات:</strong> ${supplier.notes}</p>

                    <h3 style="color: #28a745; border-bottom: 2px solid #28a745; padding-bottom: 10px; margin-top: 20px;">معلومات العقد</h3>
                    <p><strong>رقم العقد:</strong> ${supplier.contractNumber || 'غير محدد'}</p>
                    <p><strong>تاريخ العقد:</strong> ${supplier.contractDate || 'غير محدد'}</p>
                    <p><strong>قيمة العقد:</strong> ${supplier.contractValue ? supplier.contractValue + ' دينار' : 'غير محدد'}</p>
                    <p><strong>مدة العقد:</strong> ${supplier.contractDuration ? supplier.contractDuration + ' شهر' : 'غير محدد'}</p>
                    <p><strong>حالة العقد:</strong> <span style="color: ${getContractStatusColor(supplier.contractStatus)}">${supplier.contractStatus || 'غير محدد'}</span></p>

                    <h3 style="color: #dc3545; border-bottom: 2px solid #dc3545; padding-bottom: 10px; margin-top: 20px;">الملفات المرفقة</h3>
                    <p><strong>العقود:</strong><br>${contractsList}</p>

                    <p style="margin-top: 20px;"><strong>تاريخ الإضافة:</strong> ${supplier.dateAdded}</p>
                </div>
            `;

            document.getElementById('detailsModal').style.display = 'block';
        }

        // إغلاق النافذة المنبثقة
        function closeModal() {
            document.getElementById('detailsModal').style.display = 'none';
        }

        // تأجير طبلية
        function assignTable(index) {
            const supplierId = prompt('أدخل معرف المورد أو اختر من القائمة:');
            if (supplierId) {
                tables[index].status = 'مؤجرة';
                tables[index].assignedSupplier = supplierId;
                localStorage.setItem('tables', JSON.stringify(tables));
                displayTables();
                updateAllStats();
                alert('تم تأجير الطبلية بنجاح!');
            }
        }

        // إخلاء طبلية
        function releaseTable(index) {
            if (confirm('هل أنت متأكد من إخلاء هذه الطبلية؟')) {
                tables[index].status = 'متاحة';
                tables[index].assignedSupplier = '';
                localStorage.setItem('tables', JSON.stringify(tables));
                displayTables();
                updateAllStats();
                alert('تم إخلاء الطبلية بنجاح!');
            }
        }

        // ملء قائمة الموردين
        function populateSupplierSelect() {
            const select = document.getElementById('assignedSupplier');
            select.innerHTML = '<option value="">اختر المورد</option>';

            suppliers.forEach(supplier => {
                if (supplier.status === 'نشط') {
                    const option = document.createElement('option');
                    option.value = supplier.id;
                    option.textContent = supplier.name;
                    select.appendChild(option);
                }
            });
        }

        // البحث في الموردين
        searchBox.addEventListener('input', function () {
            const searchTerm = this.value.toLowerCase();
            const filtered = suppliers.filter(supplier =>
                supplier.name.toLowerCase().includes(searchTerm) ||
                supplier.company.toLowerCase().includes(searchTerm) ||
                supplier.phone.includes(searchTerm) ||
                supplier.email.toLowerCase().includes(searchTerm) ||
                supplier.category.toLowerCase().includes(searchTerm) ||
                supplier.supplierType.toLowerCase().includes(searchTerm)
            );
            displaySuppliers(filtered);
        });

        // البحث في الطبليات
        searchTables.addEventListener('input', function () {
            const searchTerm = this.value.toLowerCase();
            const filtered = tables.filter(table =>
                table.number.toLowerCase().includes(searchTerm) ||
                table.location.toLowerCase().includes(searchTerm) ||
                table.size.toLowerCase().includes(searchTerm) ||
                table.status.toLowerCase().includes(searchTerm)
            );
            displayTables(filtered);
        });

        // تحديث جميع الإحصائيات
        function updateAllStats() {
            updateSupplierStats();
            updateContractStats();
            updateTableStats();
            generateReports();
        }

        // تحديث إحصائيات الموردين
        function updateSupplierStats() {
            document.getElementById('totalSuppliers').textContent = suppliers.length;
            document.getElementById('activeSuppliers').textContent = suppliers.filter(s => s.status === 'نشط').length;
            document.getElementById('rentSuppliers').textContent = suppliers.filter(s => s.supplierType === 'إيجار').length;
            document.getElementById('percentageSuppliers').textContent = suppliers.filter(s => s.supplierType === 'نسبة خصم' || s.supplierType === 'نسبة مجاني').length;
        }

        // تحديث إحصائيات الطبليات
        function updateTableStats() {
            const totalTables = tables.length;
            const occupiedTables = tables.filter(t => t.status === 'مؤجرة').length;
            const availableTables = tables.filter(t => t.status === 'متاحة').length;
            const monthlyRevenue = tables.filter(t => t.status === 'مؤجرة').reduce((sum, t) => sum + t.monthlyRent, 0);

            document.getElementById('totalTables').textContent = totalTables;
            document.getElementById('occupiedTables').textContent = occupiedTables;
            document.getElementById('availableTables').textContent = availableTables;
            document.getElementById('monthlyRevenue').textContent = monthlyRevenue;
        }

        // إنشاء التقارير
        function generateReports() {
            const totalRevenue = tables.filter(t => t.status === 'مؤجرة').reduce((sum, t) => sum + t.monthlyRent, 0);
            const yearlyRevenue = totalRevenue * 12;
            const occupancyRate = tables.length > 0 ? Math.round((tables.filter(t => t.status === 'مؤجرة').length / tables.length) * 100) : 0;
            const avgRentPerTable = tables.length > 0 ? Math.round(tables.reduce((sum, t) => sum + t.monthlyRent, 0) / tables.length) : 0;

            document.getElementById('totalRevenue').textContent = totalRevenue;
            document.getElementById('yearlyRevenue').textContent = yearlyRevenue;
            document.getElementById('occupancyRate').textContent = occupancyRate + '%';
            document.getElementById('avgRentPerTable').textContent = avgRentPerTable;

            // التقرير المفصل
            const detailedReport = document.getElementById('detailedReport');
            detailedReport.innerHTML = `
                <div class="report-section">
                    <h3>تقرير الموردين</h3>
                    <table class="report-table">
                        <thead>
                            <tr>
                                <th>نوع المورد</th>
                                <th>العدد</th>
                                <th>النسبة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>إيجار</td>
                                <td>${suppliers.filter(s => s.supplierType === 'إيجار').length}</td>
                                <td>${suppliers.length > 0 ? Math.round((suppliers.filter(s => s.supplierType === 'إيجار').length / suppliers.length) * 100) : 0}%</td>
                            </tr>
                            <tr>
                                <td>نسبة خصم</td>
                                <td>${suppliers.filter(s => s.supplierType === 'نسبة خصم').length}</td>
                                <td>${suppliers.length > 0 ? Math.round((suppliers.filter(s => s.supplierType === 'نسبة خصم').length / suppliers.length) * 100) : 0}%</td>
                            </tr>
                            <tr>
                                <td>نسبة مجاني</td>
                                <td>${suppliers.filter(s => s.supplierType === 'نسبة مجاني').length}</td>
                                <td>${suppliers.length > 0 ? Math.round((suppliers.filter(s => s.supplierType === 'نسبة مجاني').length / suppliers.length) * 100) : 0}%</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="report-section">
                    <h3>تقرير الطبليات</h3>
                    <table class="report-table">
                        <thead>
                            <tr>
                                <th>حالة الطبلية</th>
                                <th>العدد</th>
                                <th>الإيراد الشهري</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>مؤجرة</td>
                                <td>${tables.filter(t => t.status === 'مؤجرة').length}</td>
                                <td>${tables.filter(t => t.status === 'مؤجرة').reduce((sum, t) => sum + t.monthlyRent, 0)} دينار</td>
                            </tr>
                            <tr>
                                <td>متاحة</td>
                                <td>${tables.filter(t => t.status === 'متاحة').length}</td>
                                <td>0 دينار</td>
                            </tr>
                            <tr>
                                <td>صيانة</td>
                                <td>${tables.filter(t => t.status === 'صيانة').length}</td>
                                <td>0 دينار</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            `;
        }

        // إعداد رفع الملفات
        function setupFileUpload() {
            const fileInput = document.getElementById('contractFile');
            const uploadArea = document.querySelector('.upload-area');

            if (!fileInput || !uploadArea) return;

            // منع السلوك الافتراضي للسحب والإفلات
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                uploadArea.addEventListener(eventName, preventDefaults, false);
                document.body.addEventListener(eventName, preventDefaults, false);
            });

            // تمييز منطقة الرفع عند السحب
            ['dragenter', 'dragover'].forEach(eventName => {
                uploadArea.addEventListener(eventName, highlight, false);
            });

            ['dragleave', 'drop'].forEach(eventName => {
                uploadArea.addEventListener(eventName, unhighlight, false);
            });

            // التعامل مع الملفات المسحوبة
            uploadArea.addEventListener('drop', handleDrop, false);

            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }

            function highlight(e) {
                uploadArea.classList.add('highlight');
            }

            function unhighlight(e) {
                uploadArea.classList.remove('highlight');
            }

            function handleDrop(e) {
                const dt = e.dataTransfer;
                const files = dt.files;
                handleFiles(files);
            }

            function handleFiles(files) {
                ([...files]).forEach(uploadFile);
            }

            function uploadFile(file) {
                if (file.type === 'application/pdf') {
                    // محاكاة رفع الملف
                    const reader = new FileReader();
                    reader.onload = function (e) {
                        // هنا يمكن إضافة الملف إلى المورد الحالي
                        alert(`تم رفع الملف: ${file.name}`);
                    };
                    reader.readAsDataURL(file);
                } else {
                    alert('يرجى رفع ملفات PDF فقط');
                }
            }
        }

        // إلغاء التعديل
        function cancelEdit() {
            editingSupplierIndex = -1;
            supplierForm.reset();
            document.getElementById('updateBtn').style.display = 'none';
            document.getElementById('cancelBtn').style.display = 'none';
            document.querySelector('.btn[type="submit"]').style.display = 'inline-block';
        }

        function cancelTableEdit() {
            editingTableIndex = -1;
            tableForm.reset();
            document.getElementById('updateTableBtn').style.display = 'none';
            document.getElementById('cancelTableBtn').style.display = 'none';
            document.querySelector('#tableForm .btn[type="submit"]').style.display = 'inline-block';
        }

        // إضافة مستمعي الأحداث للأزرار
        document.getElementById('cancelBtn').addEventListener('click', cancelEdit);
        document.getElementById('cancelTableBtn').addEventListener('click', cancelTableEdit);

        // إغلاق النافذة المنبثقة عند النقر خارجها
        window.addEventListener('click', function (event) {
            const modal = document.getElementById('detailsModal');
            if (event.target === modal) {
                closeModal();
            }
        });

        // تصدير البيانات
        function exportData() {
            const data = {
                suppliers: suppliers,
                tables: tables,
                exportDate: new Date().toLocaleDateString('ar-EG')
            };

            const dataStr = JSON.stringify(data, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = 'suppliers_data.json';
            link.click();
        }

        // استيراد البيانات
        function importData() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.onchange = function (event) {
                const file = event.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function (e) {
                        try {
                            const data = JSON.parse(e.target.result);
                            if (data.suppliers && data.tables) {
                                suppliers = data.suppliers;
                                tables = data.tables;
                                localStorage.setItem('suppliers', JSON.stringify(suppliers));
                                localStorage.setItem('tables', JSON.stringify(tables));
                                displaySuppliers();
                                displayTables();
                                updateAllStats();
                                populateSupplierSelect();
                                alert('تم استيراد البيانات بنجاح!');
                            } else {
                                alert('ملف البيانات غير صحيح!');
                            }
                        } catch (error) {
                            alert('خطأ في قراءة الملف!');
                        }
                    };
                    reader.readAsText(file);
                }
            };
            input.click();
        }

        // استيراد ملف الموردين Excel
        function importSuppliersExcel() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.xlsx,.xls';
            input.onchange = function (event) {
                const file = event.target.files[0];
                if (file) {
                    alert('ملاحظة: لاستيراد ملفات Excel، يرجى تحويل الملف إلى CSV أولاً، ثم نسخ البيانات ولصقها في النموذج أعلاه.\n\nأو يمكنك إدخال البيانات يدوياً من ملف "موردين.xlsx" الموجود لديك.');
                    // هنا يمكن إضافة مكتبة لقراءة Excel مثل SheetJS
                }
            };
            input.click();
        }

        // استيراد ملف العقود Excel
        function importContractsExcel() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.xlsx,.xls';
            input.onchange = function (event) {
                const file = event.target.files[0];
                if (file) {
                    alert('ملاحظة: لاستيراد ملفات Excel، يرجى تحويل الملف إلى CSV أولاً.\n\nأو يمكنك إدخال بيانات العقود يدوياً من ملف "عقود التوريد.xlsx" الموجود لديك.\n\nتأكد من ربط كل عقد بالمورد المناسب عند الإدخال.');
                    // هنا يمكن إضافة مكتبة لقراءة Excel مثل SheetJS
                }
            };
            input.click();
        }

        // دمج بيانات العقود مع الموردين
        function mergeContractData(contractData) {
            contractData.forEach(contract => {
                // البحث عن المورد المطابق
                const supplierIndex = suppliers.findIndex(supplier =>
                    supplier.name === contract.supplierName ||
                    supplier.company === contract.companyName ||
                    supplier.contractNumber === contract.contractNumber
                );

                if (supplierIndex !== -1) {
                    // تحديث بيانات العقد للمورد
                    suppliers[supplierIndex].contractNumber = contract.contractNumber;
                    suppliers[supplierIndex].contractDate = contract.contractDate;
                    suppliers[supplierIndex].contractValue = contract.contractValue;
                    suppliers[supplierIndex].contractDuration = contract.contractDuration;
                    suppliers[supplierIndex].contractStatus = contract.contractStatus;
                }
            });

            localStorage.setItem('suppliers', JSON.stringify(suppliers));
            displaySuppliers();
            updateAllStats();
            alert('تم دمج بيانات العقود بنجاح!');
        }
    </script>
</body>

</html>