<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>نظام إدارة العقود المتطور - جمعية المنقف التعاونية</title>
  
  <!-- Meta Tags -->
  <meta name="description" content="نظام إدارة عقود الموردين والإيجارات المتطور - تطوير محمد مرزوق العقاب">
  <meta name="keywords" content="عقود, موردين, إيجارات, جمعية المنقف, الكويت">
  <meta name="author" content="محمد مرزوق العقاب">
  
  <!-- CSS Libraries -->
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
  
  <!-- Tailwind Config -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          fontFamily: {
            'cairo': ['Cairo', 'sans-serif'],
          },
          colors: {
            'primary': {
              50: '#f0f9ff',
              500: '#0ea5e9',
              600: '#0284c7',
              700: '#0369a1',
              800: '#075985',
              900: '#0c4a6e',
            }
          }
        }
      }
    }
  </script>
  
  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
  
  <style>
    body {
      font-family: 'Cairo', sans-serif;
      background: linear-gradient(135deg, #0a0f1c 0%, #1e293b 50%, #334155 100%);
      min-height: 100vh;
    }
    
    .glass-card {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      box-shadow: 0 25px 45px rgba(0, 0, 0, 0.1);
    }
    
    .pro-card {
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(15px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    .pro-card:hover {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(59, 130, 246, 0.5);
      transform: translateY(-8px) scale(1.02);
      box-shadow: 0 30px 60px rgba(59, 130, 246, 0.2);
    }
    
    .pro-btn {
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      border: none;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }
    
    .pro-btn:hover {
      background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
      transform: translateY(-2px);
      box-shadow: 0 15px 35px rgba(59, 130, 246, 0.4);
    }
    
    .pro-btn-outline {
      background: transparent;
      border: 2px solid rgba(255, 255, 255, 0.3);
      color: white;
      transition: all 0.3s ease;
    }
    
    .pro-btn-outline:hover {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(59, 130, 246, 0.8);
      color: #3b82f6;
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
    }
    
    .animate-scale-in {
      animation: scaleIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
      opacity: 0;
      transform: scale(0.9) translateY(20px);
    }
    
    @keyframes scaleIn {
      to {
        opacity: 1;
        transform: scale(1) translateY(0);
      }
    }
    
    .floating-animation {
      animation: floating 6s ease-in-out infinite;
    }
    
    @keyframes floating {
      0%, 100% { transform: translateY(0px); }
      50% { transform: translateY(-10px); }
    }
    
    .gradient-text {
      background: linear-gradient(135deg, #3b82f6, #8b5cf6, #ec4899);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    
    .stats-card {
      background: rgba(255, 255, 255, 0.08);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.15);
      transition: all 0.3s ease;
    }
    
    .stats-card:hover {
      background: rgba(255, 255, 255, 0.12);
      transform: translateY(-5px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    }
    
    .notification {
      position: fixed;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 1050;
    }
    
    .modal-container {
      position: fixed;
      inset: 0;
      background-color: rgba(0, 0, 0, 0.7);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1040;
      backdrop-filter: blur(5px);
    }
    
    .modal-content {
      background: rgba(15, 23, 42, 0.95);
      border: 1px solid rgba(255, 255, 255, 0.2);
      max-width: 90vw;
      width: 100%;
      max-height: 90vh;
      display: flex;
      flex-direction: column;
    }
    
    .status-badge {
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 0.875rem;
      font-weight: 600;
    }
    
    .category-badge {
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 0.875rem;
      font-weight: 600;
    }
    
    .category-spices {
      background: rgba(245, 158, 11, 0.2);
      color: #f59e0b;
      border: 1px solid rgba(245, 158, 11, 0.3);
    }
    
    .category-consumer {
      background: rgba(16, 185, 129, 0.2);
      color: #10b981;
      border: 1px solid rgba(16, 185, 129, 0.3);
    }
    
    .category-cheese {
      background: rgba(59, 130, 246, 0.2);
      color: #3b82f6;
      border: 1px solid rgba(59, 130, 246, 0.3);
    }
  </style>
</head>
<body class="min-h-screen">
  <!-- Header -->
  <header class="glass-card rounded-none border-x-0 border-t-0 mb-8">
    <div class="container mx-auto px-6 py-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4 space-x-reverse">
          <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center floating-animation">
            <i class="fas fa-building text-white text-2xl"></i>
          </div>
          <div>
            <h1 class="text-3xl font-bold gradient-text">نظام إدارة العقود المتطور</h1>
            <p class="text-blue-200 text-lg">جمعية المنقف التعاونية</p>
          </div>
        </div>
        
        <div class="flex items-center space-x-4 space-x-reverse">
          <div class="text-center">
            <div class="text-sm text-blue-200">التاريخ الحالي</div>
            <div id="currentDate" class="text-white font-semibold"></div>
          </div>
          <div class="text-center">
            <div class="text-sm text-blue-200">الوقت الحالي</div>
            <div id="currentTime" class="text-white font-semibold"></div>
          </div>
        </div>
      </div>
    </div>
  </header>

  <!-- Main Content -->
  <div class="container mx-auto px-6">
    <!-- Welcome Section -->
    <div class="glass-card rounded-3xl p-8 mb-12 text-center animate-scale-in">
      <div class="mb-6">
        <h2 class="text-4xl font-bold text-white mb-4">مرحباً بك في النظام المتطور</h2>
        <p class="text-xl text-slate-300 leading-relaxed">
          نظام شامل ومتكامل لإدارة عقود الموردين وإيجار العيون في السوق المركزي
        </p>
      </div>
      
      <!-- Quick Stats -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mt-8">
        <div class="stats-card rounded-2xl p-6">
          <div class="text-3xl font-bold text-blue-400 mb-2" id="totalSuppliers">0</div>
          <div class="text-slate-300">إجمالي الموردين</div>
        </div>
        <div class="stats-card rounded-2xl p-6">
          <div class="text-3xl font-bold text-green-400 mb-2" id="activeContracts">0</div>
          <div class="text-slate-300">العقود النشطة</div>
        </div>
        <div class="stats-card rounded-2xl p-6">
          <div class="text-3xl font-bold text-purple-400 mb-2" id="totalEyes">0</div>
          <div class="text-slate-300">العيون المؤجرة</div>
        </div>
        <div class="stats-card rounded-2xl p-6">
          <div class="text-3xl font-bold text-yellow-400 mb-2" id="monthlyRevenue">0</div>
          <div class="text-slate-300">الإيرادات الشهرية</div>
        </div>
      </div>
    </div>

    <!-- Main Cards Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
      <!-- Suppliers Management -->
      <div class="pro-card p-8 text-center animate-scale-in" style="animation-delay: 0.1s" onclick="showSection('suppliers')">
        <div class="w-20 h-20 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
          <i class="fas fa-users text-white text-3xl"></i>
        </div>
        <h3 class="text-2xl font-bold text-white mb-4">إدارة الموردين</h3>
        <p class="text-slate-300 mb-8 leading-relaxed">إضافة وتعديل وإدارة بيانات الموردين مع تصنيفهم حسب نوع البضائع</p>
      </div>

      <!-- Contracts Management -->
      <div class="pro-card p-8 text-center animate-scale-in" style="animation-delay: 0.2s" onclick="showSection('contracts')">
        <div class="w-20 h-20 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
          <i class="fas fa-file-contract text-white text-3xl"></i>
        </div>
        <h3 class="text-2xl font-bold text-white mb-4">إدارة العقود</h3>
        <p class="text-slate-300 mb-8 leading-relaxed">إنشاء وإدارة عقود التوريد والإيجار مع متابعة تواريخ الانتهاء والتجديد</p>
      </div>

      <!-- Eyes Management -->
      <div class="pro-card p-8 text-center animate-scale-in" style="animation-delay: 0.3s" onclick="showSection('eyes')">
        <div class="w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
          <i class="fas fa-eye text-white text-3xl"></i>
        </div>
        <h3 class="text-2xl font-bold text-white mb-4">نظام العيون</h3>
        <p class="text-slate-300 mb-8 leading-relaxed">إدارة إيجار العيون والطبليات في السوق مع حساب الإيجارات تلقائياً</p>
      </div>

      <!-- Reports -->
      <div class="pro-card p-8 text-center animate-scale-in" style="animation-delay: 0.6s" onclick="showSection('reports')">
        <div class="w-20 h-20 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
          <i class="fas fa-chart-line text-white text-3xl"></i>
        </div>
        <h3 class="text-2xl font-bold text-white mb-4">التقارير والإحصائيات</h3>
        <p class="text-slate-300 mb-8 leading-relaxed">عرض تقارير مفصلة وإحصائيات شاملة عن الموردين والعقود والإيرادات</p>
      </div>
    </div>
  </div>

  <!-- Footer -->
  <footer class="glass-card rounded-none border-x-0 border-b-0 mt-16">
    <div class="container mx-auto px-6 py-8">
      <div class="text-center">
        <p class="text-blue-200 text-lg mb-2">
          © 2024 جمعية المنقف التعاونية - جميع الحقوق محفوظة
        </p>
        <p class="text-blue-300">
          <i class="fas fa-code mr-2"></i>
          تطوير وتنفيذ: <span class="font-bold text-white">محمد مرزوق العقاب</span>
        </p>
      </div>
    </div>
  </footer>

  <!-- Notification Container -->
  <div id="notification-container" class="notification"></div>

  <!-- Scripts -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      updateDateTime();
      loadDashboardStats();
      loadSuppliersData();
      setInterval(updateDateTime, 1000);
      setInterval(loadDashboardStats, 30000);
    });

    async function loadSuppliersData() {
      try {
        const suppliersData = [
          { id: 1, name: 'شركة البهارات الذهبية', category: 'بهارات', phone: '99887766', status: 'active' },
          { id: 2, name: 'مؤسسة المواد الاستهلاكية', category: 'استهلاكي', phone: '99776655', status: 'active' },
          { id: 3, name: 'شركة الأجبان الطازجة', category: 'أجبان', phone: '99665544', status: 'active' },
        ];
        localStorage.setItem('suppliersData', JSON.stringify(suppliersData));
      } catch (error) {
        showNotification('فشل في تحميل بيانات الموردين', 'error');
      }
    }

    function updateDateTime() {
      const now = new Date();
      const dateOptions = { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' };
      const timeOptions = { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false };
      document.getElementById('currentDate').textContent = now.toLocaleDateString('ar-KW', dateOptions);
      document.getElementById('currentTime').textContent = now.toLocaleTimeString('ar-KW', timeOptions);
    }

    async function loadDashboardStats() {
      try {
        const stats = { totalSuppliers: 2201, activeContracts: 67, rentedEyes: 65, monthlyRevenue: 3900 };
        animateNumber('totalSuppliers', 0, stats.totalSuppliers, 1500);
        animateNumber('activeContracts', 0, stats.activeContracts, 1200);
        animateNumber('totalEyes', 0, stats.rentedEyes, 1000);
        animateNumber('monthlyRevenue', 0, stats.monthlyRevenue, 1800, ' د.ك');
      } catch (error) {
        console.error('Error loading dashboard stats:', error);
      }
    }

    function animateNumber(elementId, start, end, duration, suffix = '') {
      const element = document.getElementById(elementId);
      if (!element) return;
      const startTime = performance.now();
      function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        const easeOut = 1 - Math.pow(1 - progress, 3);
        const current = Math.floor(start + (end - start) * easeOut);
        element.textContent = current.toLocaleString('ar-KW') + suffix;
        if (progress < 1) {
          requestAnimationFrame(updateNumber);
        }
      }
      requestAnimationFrame(updateNumber);
    }

    function showSection(sectionId) {
        switch(sectionId) {
            case 'suppliers':
                showSuppliersSection();
                break;
            case 'contracts':
                showContractsSection();
                break;
            case 'eyes':
                showEyesSection();
                break;
            case 'reports':
                showReportsSection();
                break;
        }
    }

    function showSuppliersSection() {
        const suppliers = JSON.parse(localStorage.getItem('suppliersData')) || [];
        let content = `<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">`;
        suppliers.forEach(s => {
            content += `
                <div class="pro-card p-6 rounded-2xl">
                    <div class="flex items-center justify-between mb-4">
                        <span class="category-badge category-${s.category === 'بهارات' ? 'spices' : s.category === 'أجبان' ? 'cheese' : 'consumer'}">${s.category}</span>
                        <span class="status-badge ${s.status === 'active' ? 'text-green-400' : 'text-red-400'}">${s.status === 'active' ? 'نشط' : 'غير نشط'}</span>
                    </div>
                    <h3 class="text-lg font-bold text-white mb-2">${s.name}</h3>
                    <p class="text-blue-200 text-sm">${s.phone}</p>
                </div>
            `;
        });
        content += `</div>`;
        showModal('إدارة الموردين', content);
    }

    function showContractsSection() {
        const content = `<div class="text-center text-white"><p>محتوى قسم إدارة العقود.</p></div>`;
        showModal('إدارة العقود', content);
    }

    function showEyesSection() {
        const content = `<div class="text-center text-white"><p>محتوى قسم نظام العيون.</p></div>`;
        showModal('نظام العيون', content);
    }

    function showReportsSection() {
        const content = `<div class="text-center text-white"><p>محتوى قسم التقارير والإحصائيات.</p></div>`;
        showModal('التقارير والإحصائيات', content);
    }

    function showModal(title, content) {
      const existingModal = document.querySelector('.modal-container');
      if (existingModal) existingModal.remove();

      const modal = document.createElement('div');
      modal.className = 'modal-container animate-scale-in';
      modal.innerHTML = `
        <div class="modal-content glass-card rounded-3xl max-w-4xl w-full">
          <div class="p-4 border-b border-white/10 flex justify-between items-center">
            <h2 class="text-xl font-bold text-white">${title}</h2>
            <button onclick="this.closest('.modal-container').remove()" class="text-white/70 hover:text-white text-2xl">&times;</button>
          </div>
          <div class="p-6 overflow-y-auto">${content}</div>
        </div>
      `;
      document.body.appendChild(modal);
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          modal.remove();
        }
      });
    }

    function getSectionName(section) {
      const names = {
        'suppliers': 'إدارة الموردين',
        'contracts': 'إدارة العقود',
        'eyes': 'نظام العيون',
        'reports': 'التقارير والإحصائيات'
      };
      return names[section] || section;
    }

    function showNotification(message, type = 'info') {
      const container = document.getElementById('notification-container');
      const config = {
        success: { icon: 'fa-check-circle', color: 'bg-green-500/80' },
        error: { icon: 'fa-times-circle', color: 'bg-red-500/80' },
        info: { icon: 'fa-info-circle', color: 'bg-blue-500/80' }
      };
      const typeConfig = config[type] || config.info;
      
      const notif = document.createElement('div');
      notif.className = `glass-card rounded-xl p-4 text-white flex items-center gap-4 animate-scale-in mb-3 ${typeConfig.color}`;
      notif.innerHTML = `<i class="fas ${typeConfig.icon} text-xl"></i><span>${message}</span>`;
      
      container.appendChild(notif);
      setTimeout(() => {
        notif.style.opacity = '0';
        notif.style.transform = 'translateY(-20px)';
        setTimeout(() => notif.remove(), 300);
      }, 3000);
    }
  </script>
</body>
</html>
