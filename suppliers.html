<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الموردين والطبليات - السوق المركزي</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .content {
            padding: 30px;
        }

        .form-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 30px;
        }

        .form-section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5em;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: transform 0.2s;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        }

        .btn-edit {
            background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
            color: #333;
        }

        .suppliers-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .suppliers-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            text-align: right;
            font-weight: bold;
        }

        .suppliers-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }

        .suppliers-table tr:hover {
            background-color: #f8f9fa;
        }

        .actions {
            display: flex;
            gap: 10px;
        }

        .search-box {
            width: 100%;
            padding: 12px;
            margin-bottom: 20px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }

        .stat-card h3 {
            font-size: 2em;
            margin-bottom: 5px;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 10px;
            width: 80%;
            max-width: 600px;
            position: relative;
        }

        .close {
            color: #aaa;
            float: left;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            position: absolute;
            top: 15px;
            left: 20px;
        }

        .close:hover {
            color: #000;
        }

        .tabs {
            display: flex;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 20px;
            overflow: hidden;
        }

        .tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            background: #e9ecef;
            cursor: pointer;
            border: none;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s;
        }

        .tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .file-upload {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: border-color 0.3s;
        }

        .file-upload:hover {
            border-color: #667eea;
        }

        .file-upload.dragover {
            border-color: #667eea;
            background-color: #f0f8ff;
        }

        .uploaded-files {
            margin-top: 10px;
        }

        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 4px;
            margin-bottom: 5px;
        }

        .file-item a {
            color: #667eea;
            text-decoration: none;
        }

        .file-item button {
            background: #dc3545;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 3px;
            cursor: pointer;
        }

        .tables-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .table-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            border: 2px solid #e9ecef;
        }

        .table-card.occupied {
            border-color: #28a745;
            background: #f8fff9;
        }

        .table-card.available {
            border-color: #6c757d;
            background: #f8f9fa;
        }

        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .table-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #333;
        }

        .table-status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-occupied {
            background: #28a745;
            color: white;
        }

        .status-available {
            background: #6c757d;
            color: white;
        }

        .table-info {
            margin-bottom: 15px;
        }

        .table-info p {
            margin-bottom: 5px;
            color: #666;
        }

        .revenue-info {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }

        .revenue-info strong {
            color: #1976d2;
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }

            .actions {
                flex-direction: column;
            }

            .suppliers-table {
                font-size: 12px;
            }

            .tabs {
                flex-direction: column;
            }

            .tables-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🏢 نظام إدارة الموردين والطبليات</h1>
            <p>السوق المركزي - إدارة شاملة للموردين والطبليات والعقود</p>
        </div>

        <div class="content">
            <!-- التبويبات -->
            <div class="tabs">
                <button class="tab active" onclick="showTab('suppliers')">إدارة الموردين</button>
                <button class="tab" onclick="showTab('tables')">إدارة الطبليات</button>
                <button class="tab" onclick="showTab('reports')">التقارير والإحصائيات</button>
            </div>

            <!-- تبويب الموردين -->
            <div id="suppliers-tab" class="tab-content active">
                <!-- إحصائيات الموردين -->
                <div class="stats">
                    <div class="stat-card">
                        <h3 id="totalSuppliers">0</h3>
                        <p>إجمالي الموردين</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="activeSuppliers">0</h3>
                        <p>الموردين النشطين</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="rentSuppliers">0</h3>
                        <p>موردين بالإيجار</p>
                    </div>
                    <div class="stat-card">
                        <h3 id="percentageSuppliers">0</h3>
                        <p>موردين بالنسبة</p>
                    </div>
                </div>

                <!-- نموذج إضافة مورد جديد -->
                <div class="form-section">
                    <h2>إضافة مورد جديد</h2>
                    <form id="supplierForm">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="supplierName">اسم المورد *</label>
                                <input type="text" id="supplierName" required>
                            </div>
                            <div class="form-group">
                                <label for="companyName">اسم الشركة</label>
                                <input type="text" id="companyName">
                            </div>
                            <div class="form-group">
                                <label for="phone">رقم الهاتف *</label>
                                <input type="tel" id="phone" required>
                            </div>
                            <div class="form-group">
                                <label for="email">البريد الإلكتروني</label>
                                <input type="email" id="email">
                            </div>
                            <div class="form-group">
                                <label for="category">الفئة</label>
                                <select id="category">
                                    <option value="">اختر الفئة</option>
                                    <option value="مواد غذائية">مواد غذائية</option>
                                    <option value="مواد بناء">مواد بناء</option>
                                    <option value="إلكترونيات">إلكترونيات</option>
                                    <option value="ملابس">ملابس</option>
                                    <option value="أخرى">أخرى</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="status">الحالة</label>
                                <select id="status">
                                    <option value="نشط">نشط</option>
                                    <option value="غير نشط">غير نشط</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="address">العنوان</label>
                            <textarea id="address" rows="3"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="notes">ملاحظات</label>
                            <textarea id="notes" rows="3"></textarea>
                        </div>
                        <button type="submit" class="btn">إضافة المورد</button>
                        <button type="button" class="btn btn-edit" id="updateBtn" style="display:none;">تحديث
                            المورد</button>
                        <button type="button" class="btn" id="cancelBtn" style="display:none;">إلغاء</button>
                    </form>
                </div>

                <!-- قائمة الموردين -->
                <div class="form-section">
                    <h2>قائمة الموردين</h2>
                    <input type="text" class="search-box" id="searchBox" placeholder="البحث في الموردين...">
                    <div style="overflow-x: auto;">
                        <table class="suppliers-table" id="suppliersTable">
                            <thead>
                                <tr>
                                    <th>الإجراءات</th>
                                    <th>الحالة</th>
                                    <th>الفئة</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الهاتف</th>
                                    <th>الشركة</th>
                                    <th>اسم المورد</th>
                                    <th>#</th>
                                </tr>
                            </thead>
                            <tbody id="suppliersTableBody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- نافذة منبثقة لعرض التفاصيل -->
        <div id="detailsModal" class="modal">
            <div class="modal-content">
                <span class="close">&times;</span>
                <h2>تفاصيل المورد</h2>
                <div id="supplierDetails"></div>
            </div>
        </div>

        <script>
            // بيانات الموردين
            let suppliers = JSON.parse(localStorage.getItem('suppliers')) || [];
            let editingIndex = -1;

            // عناصر DOM
            const form = document.getElementById('supplierForm');
            const tableBody = document.getElementById('suppliersTableBody');
            const searchBox = document.getElementById('searchBox');
            const modal = document.getElementById('detailsModal');
            const updateBtn = document.getElementById('updateBtn');
            const cancelBtn = document.getElementById('cancelBtn');

            // تحميل البيانات عند بدء التشغيل
            document.addEventListener('DOMContentLoaded', function () {
                displaySuppliers();
                updateStats();
            });

            // إضافة مورد جديد
            form.addEventListener('submit', function (e) {
                e.preventDefault();

                const supplier = {
                    id: editingIndex === -1 ? Date.now() : suppliers[editingIndex].id,
                    name: document.getElementById('supplierName').value,
                    company: document.getElementById('companyName').value,
                    phone: document.getElementById('phone').value,
                    email: document.getElementById('email').value,
                    category: document.getElementById('category').value,
                    status: document.getElementById('status').value,
                    address: document.getElementById('address').value,
                    notes: document.getElementById('notes').value,
                    dateAdded: editingIndex === -1 ? new Date().toLocaleDateString('ar-EG') : suppliers[editingIndex].dateAdded
                };

                if (editingIndex === -1) {
                    suppliers.push(supplier);
                } else {
                    suppliers[editingIndex] = supplier;
                    editingIndex = -1;
                    updateBtn.style.display = 'none';
                    cancelBtn.style.display = 'none';
                    document.querySelector('.btn[type="submit"]').style.display = 'inline-block';
                }

                localStorage.setItem('suppliers', JSON.stringify(suppliers));
                form.reset();
                displaySuppliers();
                updateStats();
                alert('تم حفظ بيانات المورد بنجاح!');
            });

            // عرض الموردين
            function displaySuppliers(filteredSuppliers = suppliers) {
                tableBody.innerHTML = '';

                filteredSuppliers.forEach((supplier, index) => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                    <td>
                        <div class="actions">
                            <button class="btn btn-edit" onclick="editSupplier(${suppliers.indexOf(supplier)})">تعديل</button>
                            <button class="btn btn-danger" onclick="deleteSupplier(${suppliers.indexOf(supplier)})">حذف</button>
                            <button class="btn" onclick="viewDetails(${suppliers.indexOf(supplier)})">عرض</button>
                        </div>
                    </td>
                    <td><span style="color: ${supplier.status === 'نشط' ? 'green' : 'red'}">${supplier.status}</span></td>
                    <td>${supplier.category}</td>
                    <td>${supplier.email}</td>
                    <td>${supplier.phone}</td>
                    <td>${supplier.company}</td>
                    <td><strong>${supplier.name}</strong></td>
                    <td>${suppliers.indexOf(supplier) + 1}</td>
                `;
                    tableBody.appendChild(row);
                });
            }

            // تعديل مورد
            function editSupplier(index) {
                const supplier = suppliers[index];
                editingIndex = index;

                document.getElementById('supplierName').value = supplier.name;
                document.getElementById('companyName').value = supplier.company;
                document.getElementById('phone').value = supplier.phone;
                document.getElementById('email').value = supplier.email;
                document.getElementById('category').value = supplier.category;
                document.getElementById('status').value = supplier.status;
                document.getElementById('address').value = supplier.address;
                document.getElementById('notes').value = supplier.notes;

                updateBtn.style.display = 'inline-block';
                cancelBtn.style.display = 'inline-block';
                document.querySelector('.btn[type="submit"]').style.display = 'none';

                document.getElementById('supplierName').scrollIntoView();
            }

            // إلغاء التعديل
            cancelBtn.addEventListener('click', function () {
                editingIndex = -1;
                form.reset();
                updateBtn.style.display = 'none';
                cancelBtn.style.display = 'none';
                document.querySelector('.btn[type="submit"]').style.display = 'inline-block';
            });

            // حذف مورد
            function deleteSupplier(index) {
                if (confirm('هل أنت متأكد من حذف هذا المورد؟')) {
                    suppliers.splice(index, 1);
                    localStorage.setItem('suppliers', JSON.stringify(suppliers));
                    displaySuppliers();
                    updateStats();
                    alert('تم حذف المورد بنجاح!');
                }
            }

            // عرض تفاصيل المورد
            function viewDetails(index) {
                const supplier = suppliers[index];
                const detailsDiv = document.getElementById('supplierDetails');

                detailsDiv.innerHTML = `
                <div style="line-height: 2;">
                    <p><strong>اسم المورد:</strong> ${supplier.name}</p>
                    <p><strong>اسم الشركة:</strong> ${supplier.company}</p>
                    <p><strong>رقم الهاتف:</strong> ${supplier.phone}</p>
                    <p><strong>البريد الإلكتروني:</strong> ${supplier.email}</p>
                    <p><strong>الفئة:</strong> ${supplier.category}</p>
                    <p><strong>الحالة:</strong> <span style="color: ${supplier.status === 'نشط' ? 'green' : 'red'}">${supplier.status}</span></p>
                    <p><strong>العنوان:</strong> ${supplier.address}</p>
                    <p><strong>ملاحظات:</strong> ${supplier.notes}</p>
                    <p><strong>تاريخ الإضافة:</strong> ${supplier.dateAdded}</p>
                </div>
            `;

                modal.style.display = 'block';
            }

            // البحث
            searchBox.addEventListener('input', function () {
                const searchTerm = this.value.toLowerCase();
                const filtered = suppliers.filter(supplier =>
                    supplier.name.toLowerCase().includes(searchTerm) ||
                    supplier.company.toLowerCase().includes(searchTerm) ||
                    supplier.phone.includes(searchTerm) ||
                    supplier.email.toLowerCase().includes(searchTerm) ||
                    supplier.category.toLowerCase().includes(searchTerm)
                );
                displaySuppliers(filtered);
            });

            // تحديث الإحصائيات
            function updateStats() {
                document.getElementById('totalSuppliers').textContent = suppliers.length;
                document.getElementById('activeSuppliers').textContent = suppliers.filter(s => s.status === 'نشط').length;

                const categories = [...new Set(suppliers.map(s => s.category).filter(c => c))];
                document.getElementById('totalCategories').textContent = categories.length;
            }

            // إغلاق النافذة المنبثقة
            document.querySelector('.close').addEventListener('click', function () {
                modal.style.display = 'none';
            });

            window.addEventListener('click', function (event) {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        </script>
</body>

</html>