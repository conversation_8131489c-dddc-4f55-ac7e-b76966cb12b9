<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم إدارة الإيجارات (نسخة مفعلة)</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Google Fonts: Cairo for Arabic -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">

    <!-- SheetJS for Excel/CSV parsing -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

    <style>
        /* Apply Cairo font to the body */
        body {
            font-family: 'Cairo', sans-serif;
        }

        /* Custom scrollbar for better aesthetics */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #0f172a;
            /* slate-900 */
        }

        ::-webkit-scrollbar-thumb {
            background: #334155;
            /* slate-700 */
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #475569;
            /* slate-600 */
        }

        /* Style for the modal backdrop */
        .modal-backdrop {
            background-color: rgba(0, 0, 0, 0.7);
            transition: opacity 0.3s ease;
        }

        /* Transition for modal content */
        .modal-content {
            transition: transform 0.3s ease;
        }

        /* Style for active navigation link */
        .nav-link.active {
            background-color: #2563eb;
            /* blue-600 */
            color: white;
            box-shadow: 0 4px 14px 0 rgb(37 99 235 / 39%);
        }

        /* Style for selected item in a list */
        .list-item.selected {
            background-color: #1e40af;
            /* blue-800 */
            border-color: #3b82f6;
            /* blue-500 */
        }

        /* Hide scrollbar for sidebar */
        .sidebar-scroll::-webkit-scrollbar {
            display: none;
        }

        .sidebar-scroll {
            -ms-overflow-style: none;
            /* IE and Edge */
            scrollbar-width: none;
            /* Firefox */
        }
    </style>
</head>

<body class="bg-slate-950 text-slate-300">

    <div class="flex h-screen">
        <!-- Sidebar Navigation -->
        <aside class="w-64 bg-slate-900 flex flex-col p-4 border-l border-slate-800 sidebar-scroll">
            <div class="shrink-0 flex items-center mb-8">
                <svg class="h-8 w-auto text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                    stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round"
                        d="M3.75 21h16.5M4.5 3h15M5.25 3v18m13.5-18v18M9 6.75h6M9 11.25h6m-6 4.5h6M5.25 6h.008v.008H5.25V6Zm.75 4.5h.008v.008H6v-.008Zm-.75 4.5h.008v.008H5.25v-.008Zm13.5-9h.008v.008h-.008V6Zm.75 4.5h.008v.008h-.008v-.008Zm-.75 4.5h.008v.008h-.008v-.008Z" />
                </svg>
                <h1 class="text-xl font-bold text-white mr-3">إدارة السوق</h1>
            </div>

            <nav class="flex flex-col space-y-2">
                <a href="#" data-target="page-dashboard"
                    class="nav-link flex items-center py-3 px-4 rounded-lg transition-colors duration-200 hover:bg-slate-800">
                    <svg class="h-5 w-5 ml-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                        stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M3.75 6A2.25 2.25 0 0 1 6 3.75h2.25A2.25 2.25 0 0 1 10.5 6v2.25a2.25 2.25 0 0 1-2.25 2.25H6a2.25 2.25 0 0 1-2.25-2.25V6ZM3.75 15.75A2.25 2.25 0 0 1 6 13.5h2.25a2.25 2.25 0 0 1 2.25 2.25V18a2.25 2.25 0 0 1-2.25 2.25H6a2.25 2.25 0 0 1-2.25-2.25v-2.25ZM13.5 6a2.25 2.25 0 0 1 2.25-2.25H18A2.25 2.25 0 0 1 20.25 6v2.25A2.25 2.25 0 0 1 18 10.5h-2.25a2.25 2.25 0 0 1-2.25-2.25V6ZM13.5 15.75a2.25 2.25 0 0 1 2.25-2.25H18a2.25 2.25 0 0 1 2.25 2.25V18A2.25 2.25 0 0 1 18 20.25h-2.25A2.25 2.25 0 0 1 13.5 18v-2.25Z" />
                    </svg>
                    <span>لوحة التحكم</span>
                </a>
                <a href="#" data-target="page-suppliers"
                    class="nav-link flex items-center py-3 px-4 rounded-lg transition-colors duration-200 hover:bg-slate-800">
                    <svg class="h-5 w-5 ml-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                        stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m-7.284.004A3 3 0 0 1 7.5 15.75c-1.657 0-3 1.343-3 3s1.343 3 3 3a3 3 0 0 1 3-3c0 .252-.036.494-.102.72m-7.284-.004c2.82 0 5.234 1.685 6.355 4.019M15.75 9a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                    </svg>
                    <span>إدارة الموردين</span>
                </a>
                <a href="#" data-target="page-contracts"
                    class="nav-link flex items-center py-3 px-4 rounded-lg transition-colors duration-200 hover:bg-slate-800">
                    <svg class="h-5 w-5 ml-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                        stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
                    </svg>
                    <span>إدارة العقود</span>
                </a>
                <a href="#" data-target="page-reports"
                    class="nav-link flex items-center py-3 px-4 rounded-lg transition-colors duration-200 hover:bg-slate-800">
                    <svg class="h-5 w-5 ml-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                        stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M10.5 6a7.5 7.5 0 1 0 7.5 7.5h-7.5V6Z" />
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M13.5 10.5H21A7.5 7.5 0 0 0 13.5 3v7.5Z" />
                    </svg>
                    <span>التقارير والمتابعة</span>
                </a>
            </nav>
            <div class="mt-auto shrink-0">
                <button id="add-contract-btn"
                    class="w-full flex items-center justify-center bg-blue-600 text-white font-bold py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors shadow-lg shadow-blue-900/40">
                    <svg class="h-5 w-5 ml-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                        stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                    </svg>
                    <span>عقد جديد</span>
                </button>
            </div>
        </aside>

        <!-- Main Content -->
        <div class="flex-1 overflow-y-auto sidebar-scroll p-6">
            <main>
                <!-- Page: Dashboard -->
                <div id="page-dashboard" class="page-content">
                    <div class="mb-8">
                        <div class="flex flex-wrap gap-6">
                            <div
                                class="bg-slate-900 border border-slate-800 rounded-lg p-6 flex-1 min-w-[220px] shadow-md flex items-center gap-4">
                                <i class="fa fa-users text-3xl text-blue-400"></i>
                                <div>
                                    <div class="text-lg font-bold text-white">عدد الموردين</div>
                                    <div id="dashboard-suppliers-count" class="text-2xl font-mono text-blue-300">0</div>
                                </div>
                            </div>
                            <div
                                class="bg-slate-900 border border-slate-800 rounded-lg p-6 flex-1 min-w-[220px] shadow-md flex items-center gap-4">
                                <i class="fa fa-file-contract text-3xl text-green-400"></i>
                                <div>
                                    <div class="text-lg font-bold text-white">عدد العقود</div>
                                    <div id="dashboard-contracts-count" class="text-2xl font-mono text-green-300">0
                                    </div>
                                </div>
                            </div>
                            <div
                                class="bg-slate-900 border border-slate-800 rounded-lg p-6 flex-1 min-w-[220px] shadow-md flex items-center gap-4">
                                <i class="fa fa-money-bill-wave text-3xl text-yellow-400"></i>
                                <div>
                                    <div class="text-lg font-bold text-white">إيراد شهري متوقع</div>
                                    <div id="dashboard-monthly-revenue" class="text-2xl font-mono text-yellow-300">0
                                        دينار</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="dashboard-notifications" class="mb-8">
                        <!-- إشعارات وتنبيهات هامة -->
                    </div>
                </div>
                <!-- Page: Dashboard -->
                <div id="page-dashboard" class="page-content">
                    <!-- Financial Summary Section -->
                    <div id="financial-summary"
                        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6 mb-8">
                        <div class="bg-slate-900 p-5 rounded-lg shadow-md border border-slate-800">
                            <h3 class="font-semibold text-slate-400">إجمالي الإيراد الشهري</h3>
                            <p id="monthly-revenue" class="text-2xl font-bold text-green-400 mt-2">0.00 دينار</p>
                        </div>
                        <div class="bg-slate-900 p-5 rounded-lg shadow-md border border-slate-800">
                            <h3 class="font-semibold text-slate-400">إجمالي الإيراد السنوي</h3>
                            <p id="annual-revenue" class="text-2xl font-bold text-blue-400 mt-2">0.00 دينار</p>
                        </div>
                        <div class="bg-slate-900 p-5 rounded-lg shadow-md border border-slate-800">
                            <h3 class="font-semibold text-slate-400">إجمالي الدعم الشهري</h3>
                            <p id="total-support" class="text-2xl font-bold text-yellow-400 mt-2">0.00 دينار</p>
                        </div>
                        <div class="bg-slate-900 p-5 rounded-lg shadow-md border border-slate-800">
                            <h3 class="font-semibold text-slate-400">الطبليات المؤجرة</h3>
                            <p id="rented-pallets" class="text-2xl font-bold text-purple-400 mt-2">0 / 60</p>
                        </div>
                        <div class="bg-slate-900 p-5 rounded-lg shadow-md border border-slate-800">
                            <h3 class="font-semibold text-slate-400">نسبة الإشغال</h3>
                            <p id="occupancy-rate" class="text-2xl font-bold text-orange-400 mt-2">0%</p>
                        </div>
                    </div>

                    <!-- Color Legend -->
                    <div class="flex flex-wrap justify-center mb-8 gap-x-4 gap-y-2 text-sm text-slate-400">
                        <span class="flex items-center"><span
                                class="w-3 h-3 rounded-full bg-slate-600 ml-2"></span>شاغر</span>
                        <span class="flex items-center"><span
                                class="w-3 h-3 rounded-full bg-green-500 ml-2"></span>مؤجر</span>
                        <span class="flex items-center"><span
                                class="w-3 h-3 rounded-full bg-yellow-400 ml-2"></span>فترة مجانية</span>
                        <span class="flex items-center"><span class="w-3 h-3 rounded-full bg-red-500 ml-2"></span>متأخر
                            الدفع</span>
                    </div>

                    <!-- Dashboards Container -->
                    <div>
                        <!-- Spices Section -->
                        <section id="spices-section" class="mb-12">
                            <h2 class="text-2xl font-bold mb-4 border-b-2 border-orange-500 pb-2 text-orange-400">قسم
                                البهارات (استاندات وجندولات)</h2>
                            <div id="spices-dashboard"
                                class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
                            </div>
                        </section>

                        <!-- Consumables Section -->
                        <section id="consumables-section">
                            <h2 class="text-2xl font-bold mb-4 border-b-2 border-teal-500 pb-2 text-teal-400">قسم
                                الاستهلاكي (طبالي)</h2>
                            <div id="pallets-dashboard"
                                class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
                            </div>
                        </section>
                    </div>
                </div>

                <!-- Page: Suppliers -->
                <div id="page-suppliers" class="page-content hidden">
                    <section class="bg-slate-900 p-6 rounded-lg shadow-md mb-6 border border-slate-800">
                        <h2 class="text-2xl font-bold mb-4 text-white">إضافة مورد جديد</h2>
                        <form id="supplier-form" class="space-y-4 md:space-y-0 md:flex md:items-end md:gap-4">
                            <div class="flex-1">
                                <label for="supplier-code-input"
                                    class="block text-sm font-medium text-slate-300 mb-1">كود المورد</label>
                                <input type="text" id="supplier-code-input" placeholder="مثال: 1001"
                                    class="w-full p-2 border bg-slate-800 border-slate-700 rounded-md text-white"
                                    required>
                            </div>
                            <div class="flex-1">
                                <label for="supplier-name-input"
                                    class="block text-sm font-medium text-slate-300 mb-1">اسم المورد</label>
                                <input type="text" id="supplier-name-input" placeholder="مثال: شركة المواد الغذائية"
                                    class="w-full p-2 border bg-slate-800 border-slate-700 rounded-md text-white"
                                    required>
                            </div>
                            <button type="submit"
                                class="w-full md:w-auto bg-green-600 text-white font-bold py-2 px-6 rounded-lg hover:bg-green-700 transition-colors">حفظ
                                المورد</button>
                        </form>
                    </section>

                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <section class="lg:col-span-1 bg-slate-900 p-6 rounded-lg shadow-md border border-slate-800">
                            <div class="flex flex-col md:flex-row justify-between md:items-center mb-4">
                                <h2 class="text-2xl font-bold text-white mb-3 md:mb-0">قائمة الموردين</h2>
                                <button id="import-files-btn"
                                    class="w-full md:w-auto bg-teal-600 text-white font-bold py-2 px-4 rounded-lg hover:bg-teal-700 transition-colors text-sm">
                                    <span>استيراد ملف</span>
                                </button>
                                <input type="file" id="file-input" class="hidden"
                                    accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel, .json">
                            </div>
                            <div id="suppliers-list" class="mt-4 max-h-[60vh] overflow-y-auto space-y-2"></div>
                        </section>

                        <section id="supplier-details-section"
                            class="lg:col-span-2 hidden bg-slate-900 p-6 rounded-lg shadow-md border border-blue-800">
                            <div class="space-y-6">
                                <!-- Supplier Edit Form -->
                                <div>
                                    <h2 class="text-xl font-bold mb-4 text-blue-300 border-b border-slate-700 pb-2">
                                        تفاصيل المورد</h2>
                                    <form id="edit-supplier-form" class="space-y-4">
                                        <input type="hidden" id="edit-supplier-code">
                                        <div>
                                            <label for="edit-supplier-name"
                                                class="block text-sm font-medium text-slate-300 mb-1">اسم المورد</label>
                                            <input type="text" id="edit-supplier-name"
                                                class="w-full p-2 border bg-slate-800 border-slate-700 rounded-md text-white">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-slate-300 mb-1">ملف المورد
                                                (PDF)</label>
                                            <label class="w-full">
                                                <span
                                                    class="w-full text-center cursor-pointer bg-blue-600 text-white font-bold py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors block">
                                                    رفع ملف المورد (PDF)
                                                </span>
                                                <input type="file" id="supplier-pdf-input" class="hidden" accept=".pdf">
                                            </label>
                                            <div id="supplier-pdf-display" class="mt-3 text-sm"></div>
                                        </div>
                                        <div class="flex justify-between items-center pt-4">
                                            <button type="submit"
                                                class="bg-green-600 text-white font-bold py-2 px-6 rounded-lg hover:bg-green-700 transition-colors">حفظ
                                                التغييرات</button>
                                            <button type="button" id="delete-supplier-btn"
                                                class="bg-red-600 text-white font-bold py-2 px-6 rounded-lg hover:bg-red-700 transition-colors">حذف
                                                المورد</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </section>
                    </div>
                </div>

                <!-- Page: Contracts -->
                <div id="page-contracts" class="page-content hidden">
                    <section class="bg-slate-900 p-6 rounded-lg shadow-md border border-slate-800">
                        <div class="flex justify-between items-center mb-4">
                            <h2 class="text-2xl font-bold text-white">جميع العقود</h2>
                            <input type="text" id="contracts-search-input"
                                placeholder="ابحث بالاسم، الكود، أو الوحدة..."
                                class="w-1/2 p-2 border bg-slate-800 border-slate-700 rounded-md text-white">
                        </div>
                        <div id="contracts-list" class="mt-4 max-h-[60vh] overflow-y-auto space-y-3">
                            <!-- Contract items will be injected here -->
                        </div>
                    </section>
                </div>

                <!-- Page: Reports -->
                <div id="page-reports" class="page-content hidden">
                    <section class="bg-slate-900 p-6 rounded-lg shadow-md mb-12 border border-slate-800">
                        <div class="flex justify-between items-center mb-4">
                            <h2 class="text-2xl font-bold text-white">التقارير والمتابعة</h2>
                            <button id="print-reports-btn"
                                class="bg-slate-600 text-white font-bold py-2 px-4 rounded-lg hover:bg-slate-700 flex items-center">
                                <svg class="h-5 w-5 ml-2" xmlns="http://www.w3.org/2000/svg" fill="none"
                                    viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round"
                                        d="M6.72 13.829c-.24.03-.48.062-.72.096m.72-.096a42.415 42.415 0 0 1 10.56 0m-10.56 0L6 3.125A2.25 2.25 0 0 1 8.25 1.5h7.5A2.25 2.25 0 0 1 18 3.125l.75 10.704M12 17.25h.008v.008H12v-.008Z" />
                                </svg>
                                <span>طباعة</span>
                            </button>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                            <div>
                                <h3 class="text-lg font-semibold mb-2 text-slate-300">عقود على وشك الانتهاء (30 يوم)
                                </h3>
                                <div id="expiring-contracts" class="mt-4 max-h-60 overflow-y-auto space-y-2"></div>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold mb-2 text-slate-300">العقود المتأخرة الدفع</h3>
                                <div id="due-contracts" class="mt-4 max-h-60 overflow-y-auto space-y-2"></div>
                            </div>
                        </div>
                    </section>

                    <!-- قسم تفاصيل الدعم والطبليات -->
                    <section class="bg-slate-900 p-6 rounded-lg shadow-md mb-12 border border-slate-800">
                        <h2 class="text-2xl font-bold text-white mb-6">تفاصيل الدعم والطبليات</h2>

                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                            <!-- إجمالي الدعم -->
                            <div class="bg-slate-800 p-4 rounded-lg border border-slate-700">
                                <h3 class="text-lg font-semibold text-yellow-400 mb-2">إجمالي الدعم الشهري</h3>
                                <p id="report-total-support" class="text-2xl font-bold text-white">0.00 دينار</p>
                                <p class="text-sm text-slate-400 mt-1">جميع أنواع الخصومات والمجاني</p>
                            </div>

                            <!-- الطبليات المؤجرة -->
                            <div class="bg-slate-800 p-4 rounded-lg border border-slate-700">
                                <h3 class="text-lg font-semibold text-purple-400 mb-2">الطبليات المؤجرة</h3>
                                <p id="report-rented-pallets" class="text-2xl font-bold text-white">0 / 60</p>
                                <p class="text-sm text-slate-400 mt-1">من إجمالي 15 طبلية × 4 عيون</p>
                            </div>

                            <!-- نسبة الإشغال -->
                            <div class="bg-slate-800 p-4 rounded-lg border border-slate-700">
                                <h3 class="text-lg font-semibold text-orange-400 mb-2">نسبة إشغال الطبليات</h3>
                                <p id="report-pallet-occupancy" class="text-2xl font-bold text-white">0%</p>
                                <p class="text-sm text-slate-400 mt-1">نسبة الطبليات المؤجرة</p>
                            </div>
                        </div>

                        <!-- تفاصيل أنواع الدعم -->
                        <div class="bg-slate-800 p-4 rounded-lg border border-slate-700">
                            <h3 class="text-lg font-semibold text-slate-300 mb-4">تفاصيل أنواع الدعم</h3>
                            <div id="support-breakdown" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                                <div class="text-center">
                                    <p class="text-sm text-slate-400">دعم حكومي</p>
                                    <p id="support-government" class="text-lg font-bold text-yellow-400">0.00</p>
                                </div>
                                <div class="text-center">
                                    <p class="text-sm text-slate-400">خصم السوق</p>
                                    <p id="support-market-discount" class="text-lg font-bold text-blue-400">0.00</p>
                                </div>
                                <div class="text-center">
                                    <p class="text-sm text-slate-400">خصم الفروع</p>
                                    <p id="support-branches-discount" class="text-lg font-bold text-green-400">0.00</p>
                                </div>
                                <div class="text-center">
                                    <p class="text-sm text-slate-400">مجاني السوق</p>
                                    <p id="support-market-free" class="text-lg font-bold text-red-400">0.00</p>
                                </div>
                                <div class="text-center">
                                    <p class="text-sm text-slate-400">مجاني الفروع</p>
                                    <p id="support-branches-free" class="text-lg font-bold text-purple-400">0.00</p>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
            </main>
        </div>
    </div>

    <!-- Modal for adding/editing contracts -->
    <div id="contract-modal" class="fixed inset-0 z-50 flex items-center justify-center p-4 modal-backdrop hidden">
        <div id="modal-content"
            class="bg-slate-800 text-slate-300 rounded-lg shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto modal-content transform scale-95">
            <div class="sticky top-0 bg-slate-800 p-5 border-b border-slate-700 flex justify-between items-center">
                <h2 id="modal-title" class="text-2xl font-bold text-white">إضافة عقد جديد</h2>
                <button id="close-modal-btn" class="text-slate-400 hover:text-red-500 text-3xl">&times;</button>
            </div>
            <form id="contract-form" class="p-6 space-y-4">
                <input type="hidden" id="contract-id">

                <!-- Unit Selection -->
                <fieldset id="unit-selection-fieldset" class="border border-slate-600 p-4 rounded-md">
                    <legend class="px-2 font-semibold">اختيار الوحدة</legend>
                    <div>
                        <label for="section-type" class="block font-medium mb-1">القسم</label>
                        <select id="section-type"
                            class="w-full p-2 border bg-slate-700 border-slate-600 rounded-md text-white"></select>
                    </div>
                    <div id="consumable-fields">
                        <div class="mt-2">
                            <label for="pallet-number" class="block font-medium mb-1">رقم الطبلية</label>
                            <select id="pallet-number"
                                class="w-full p-2 border bg-slate-700 border-slate-600 rounded-md text-white"></select>
                        </div>
                        <div class="mt-2">
                            <label for="slot-number" class="block font-medium mb-1">رقم العين (1-4)</label>
                            <select id="slot-number"
                                class="w-full p-2 border bg-slate-700 border-slate-600 rounded-md text-white"></select>
                        </div>
                    </div>
                    <div id="spice-fields" class="hidden space-y-2 mt-2">
                        <div>
                            <label for="spice-unit-type" class="block font-medium mb-1">نوع الوحدة</label>
                            <select id="spice-unit-type"
                                class="w-full p-2 border bg-slate-700 border-slate-600 rounded-md text-white"></select>
                        </div>
                        <div id="stand-fields">
                            <label for="stand-id" class="block font-medium mb-1">اختر الاستاند</label>
                            <select id="stand-id"
                                class="w-full p-2 border bg-slate-700 border-slate-600 rounded-md text-white"></select>
                            <label for="stand-length" class="block font-medium mt-2 mb-1">الطول المطلوب (متر)</label>
                            <input type="number" id="stand-length" min="1"
                                class="w-full p-2 border bg-slate-700 border-slate-600 rounded-md text-white"
                                placeholder="مثال: 3">
                        </div>
                        <div id="gondola-fields" class="hidden">
                            <label for="gondola-id" class="block font-medium mb-1">اختر الجندوله</label>
                            <select id="gondola-id"
                                class="w-full p-2 border bg-slate-700 border-slate-600 rounded-md text-white"></select>
                        </div>
                    </div>
                </fieldset>

                <!-- Supplier and Dates -->
                <fieldset class="border border-slate-600 p-4 rounded-md">
                    <legend class="px-2 font-semibold">تفاصيل العقد الأساسية</legend>
                    <div>
                        <label for="supplier-select" class="block font-medium mb-1">اختر المورد</label>
                        <select id="supplier-select"
                            class="w-full p-2 border bg-slate-700 border-slate-600 rounded-md text-white"
                            required></select>
                    </div>
                    <div class="grid md:grid-cols-2 gap-4 mt-4">
                        <div>
                            <label for="start-date" class="block font-medium mb-1">تاريخ بدء العقد</label>
                            <input type="date" id="start-date"
                                class="w-full p-2 border bg-slate-700 border-slate-600 rounded-md text-slate-300"
                                required>
                        </div>
                        <div>
                            <label for="end-date" class="block font-medium mb-1">تاريخ انتهاء العقد</label>
                            <input type="date" id="end-date"
                                class="w-full p-2 border bg-slate-700 border-slate-600 rounded-md text-slate-300"
                                required>
                        </div>
                    </div>
                </fieldset>

                <!-- Notes from Import -->
                <fieldset id="notes-fieldset" class="hidden border border-slate-600 p-4 rounded-md">
                    <legend class="px-2 font-semibold">ملاحظات من الاستيراد</legend>
                    <textarea id="contract-notes" rows="3"
                        class="w-full p-2 border bg-slate-700 border-slate-600 rounded-md text-white"
                        placeholder="تفاصيل إضافية من ملف الاستيراد..."></textarea>
                </fieldset>

                <!-- Rent and Discounts -->
                <fieldset class="border border-slate-600 p-4 rounded-md">
                    <legend class="px-2 font-semibold">الإيجار والخصومات</legend>
                    <div class="grid md:grid-cols-2 gap-4">
                        <div>
                            <label for="monthly-rent" class="block font-medium mb-1">قيمة الإيجار الشهري</label>
                            <input type="number" id="monthly-rent"
                                class="w-full p-2 border bg-slate-600 border-slate-500 rounded-md text-white" readonly
                                required>
                        </div>
                        <div>
                            <label for="support-discount" class="block font-medium mb-1">دعم (خصم نقدي %)</label>
                            <input type="number" id="support-discount" value="0" min="0" max="100"
                                class="w-full p-2 border bg-slate-700 border-slate-600 rounded-md text-white">
                        </div>
                    </div>
                    <div class="grid md:grid-cols-2 gap-4 mt-4">
                        <div>
                            <label for="discount_market" class="block font-medium mb-1">نسبة خصم - سوق (%)</label>
                            <input type="number" id="discount_market" value="0" min="0" max="100"
                                class="w-full p-2 border bg-slate-700 border-slate-600 rounded-md text-white">
                        </div>
                        <div>
                            <label for="discount_branches" class="block font-medium mb-1">نسبة خصم - فروع (%)</label>
                            <input type="number" id="discount_branches" value="0" min="0" max="100"
                                class="w-full p-2 border bg-slate-700 border-slate-600 rounded-md text-white">
                        </div>
                    </div>
                </fieldset>

                <!-- Freebies and Collection -->
                <fieldset class="border border-slate-600 p-4 rounded-md">
                    <legend class="px-2 font-semibold">العروض المجانية والتحصيل</legend>
                    <div class="grid md:grid-cols-2 gap-4">
                        <div>
                            <label for="free_market" class="block font-medium mb-1">نسبة مجاني - سوق (%)</label>
                            <input type="number" id="free_market" value="0" min="0" max="100"
                                class="w-full p-2 border bg-slate-700 border-slate-600 rounded-md text-white">
                        </div>
                        <div>
                            <label for="free_branches" class="block font-medium mb-1">نسبة مجاني - فروع (%)</label>
                            <input type="number" id="free_branches" value="0" min="0" max="100"
                                class="w-full p-2 border bg-slate-700 border-slate-600 rounded-md text-white">
                        </div>
                    </div>
                    <div class="mt-4">
                        <label for="collection-method" class="block font-medium mb-1">طريقة التحصيل</label>
                        <select id="collection-method"
                            class="w-full p-2 border bg-slate-700 border-slate-600 rounded-md text-white">
                            <option value="نقدي">نقدي</option>
                            <option value="خصم من الحساب">خصم من الحساب</option>
                            <option value="ربع سنوي">ربع سنوي</option>
                            <option value="نص سنوي">نص سنوي</option>
                            <option value="سنوي">سنوي</option>
                        </select>
                    </div>
                </fieldset>

                <div>
                    <label class="block font-semibold mb-1">حالة السداد</label>
                    <div class="flex items-center gap-4">
                        <label><input type="radio" name="paymentStatus" value="paid"
                                class="ml-2 focus:ring-blue-500 text-blue-600 border-gray-300">مسدد</label>
                        <label><input type="radio" name="paymentStatus" value="due"
                                class="ml-2 focus:ring-blue-500 text-blue-600 border-gray-300" checked>مستحق</label>
                    </div>
                </div>
                <div class="pt-4 flex justify-end gap-3">
                    <button type="button" id="delete-contract-btn"
                        class="bg-red-600 text-white font-bold py-2 px-5 rounded-lg hover:bg-red-700 transition-colors hidden">حذف
                        العقد</button>
                    <button type="submit"
                        class="bg-green-600 text-white font-bold py-2 px-5 rounded-lg hover:bg-green-700 transition-colors">حفظ
                        العقد</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Confirmation Modal -->
    <div id="confirm-modal" class="fixed inset-0 z-60 flex items-center justify-center p-4 modal-backdrop hidden">
        <div class="bg-slate-800 rounded-lg shadow-2xl w-full max-w-sm">
            <div class="p-6 text-center">
                <h3 id="confirm-title" class="text-lg font-semibold text-white mb-2">تأكيد الإجراء</h3>
                <p id="confirm-text" class="text-slate-300 mb-6">هل أنت متأكد؟</p>
                <div class="flex justify-center gap-4">
                    <button id="confirm-cancel-btn"
                        class="py-2 px-6 bg-slate-600 text-white rounded-lg hover:bg-slate-700">إلغاء</button>
                    <button id="confirm-ok-btn"
                        class="py-2 px-6 bg-red-600 text-white rounded-lg hover:bg-red-700">تأكيد</button>
                </div>
            </div>
        </div>
    </div>


    <!-- App Logic -->
    <script>
        document.addEventListener('DOMContentLoaded', () => {

            // --- CONSTANTS ---
            const RENTAL_CONFIG = {
                consumable: { pallets: 15 },
                spice: { stands: 5, gondolas: 10 },
                prices: {
                    palletSlot: 60,
                    standPerMeter: 2000,
                    gondola: 1800
                }
            };

            // --- STATE ---
            let contractsData = [];
            let suppliersData = [];
            let tempSupplierPdf = { dataUrl: null, name: null };


            // --- DOM ELEMENTS ---
            const palletsDashboard = document.getElementById('pallets-dashboard');
            const spicesDashboard = document.getElementById('spices-dashboard');
            const modal = document.getElementById('contract-modal');
            const modalContent = document.getElementById('modal-content');
            const closeModalBtn = document.getElementById('close-modal-btn');
            const addContractBtn = document.getElementById('add-contract-btn');
            const contractForm = document.getElementById('contract-form');
            const modalTitle = document.getElementById('modal-title');
            const deleteContractBtn = document.getElementById('delete-contract-btn');
            const sectionTypeSelect = document.getElementById('section-type');
            const spiceUnitTypeSelect = document.getElementById('spice-unit-type');
            const standLengthInput = document.getElementById('stand-length');
            const monthlyRentInput = document.getElementById('monthly-rent');
            const contractsList = document.getElementById('contracts-list');
            const contractsSearchInput = document.getElementById('contracts-search-input');
            const expiringContractsContainer = document.getElementById('expiring-contracts');
            const dueContractsContainer = document.getElementById('due-contracts');
            const supplierForm = document.getElementById('supplier-form');
            const supplierCodeInput = document.getElementById('supplier-code-input');
            const supplierNameInput = document.getElementById('supplier-name-input');
            const suppliersListContainer = document.getElementById('suppliers-list');
            const supplierSelect = document.getElementById('supplier-select');
            const importFilesBtn = document.getElementById('import-files-btn');
            const fileInput = document.getElementById('file-input');
            const printReportsBtn = document.getElementById('print-reports-btn');
            const navLinks = document.querySelectorAll('.nav-link');
            const pages = document.querySelectorAll('.page-content');
            const unitSelectionFieldset = document.getElementById('unit-selection-fieldset');
            const notesFieldset = document.getElementById('notes-fieldset');
            const contractNotesTextarea = document.getElementById('contract-notes');

            // Supplier Details Elements
            const supplierDetailsSection = document.getElementById('supplier-details-section');
            const editSupplierForm = document.getElementById('edit-supplier-form');
            const editSupplierCodeInput = document.getElementById('edit-supplier-code');
            const editSupplierNameInput = document.getElementById('edit-supplier-name');
            const supplierPdfInput = document.getElementById('supplier-pdf-input');
            const supplierPdfDisplay = document.getElementById('supplier-pdf-display');
            const deleteSupplierBtn = document.getElementById('delete-supplier-btn');


            // --- DATA HANDLING & INITIALIZATION ---
            function getSampleData() {
                const today = new Date();
                const currentYear = today.getFullYear();
                const nextYear = currentYear + 1;
                const lastYear = currentYear - 1;

                const sampleSuppliers = [
                    { code: '1001', name: 'شركة الفجرى الوطنية للتجارة العامة', pdfDataUrl: null, pdfFilename: null },
                    { code: '1002', name: 'الشركة الكويتية المتحدة للدواجن', pdfDataUrl: null, pdfFilename: null },
                    { code: '1003', name: 'شركة مطاحن الدقيق والمخابز الكويتية', pdfDataUrl: null, pdfFilename: null },
                    { code: '1004', name: 'شركة العثمان والبشر للتجارة', pdfDataUrl: null, pdfFilename: null }
                ];

                const sampleContracts = [
                    { id: 'c1', supplierCode: '1001', type: 'pallet', unitId: 'استهلاكي-1', slot: 1, startDate: `${currentYear}-01-01`, endDate: `${nextYear}-01-01`, monthlyRent: '60.00', supportDiscount: '0', paymentStatus: 'due', notes: '' },
                    { id: 'c2', supplierCode: '1002', type: 'stand', unitId: 'استاند-3', length: 5, startDate: `${lastYear}-12-01`, endDate: `${currentYear}-11-30`, monthlyRent: '10000.00', supportDiscount: '10', paymentStatus: 'paid', notes: 'خصم خاص بنسبة 5% للسوق والفروع' },
                    { id: 'c3', supplierCode: '1003', type: 'gondola', unitId: 'جندوله-5', startDate: `${currentYear}-06-15`, endDate: `${nextYear}-06-14`, monthlyRent: '1800.00', supportDiscount: '0', free_market: '100', paymentStatus: 'paid', notes: 'فترة مجانية للسوق فقط' },
                    { id: 'c4', supplierCode: '1004', type: 'pallet', unitId: 'استهلاكي-8', slot: 3, startDate: `${lastYear}-08-10`, endDate: today.toISOString().split('T')[0], monthlyRent: '60.00', supportDiscount: '0', paymentStatus: 'paid', notes: '' }
                ];
                return { sampleSuppliers, sampleContracts };
            }

            function loadAllDataFromLocal() {
                const localContracts = localStorage.getItem('marketContractsData');
                const localSuppliers = localStorage.getItem('marketSuppliersData');

                if (!localContracts && !localSuppliers) {
                    const { sampleSuppliers, sampleContracts } = getSampleData();
                    suppliersData = sampleSuppliers;
                    contractsData = sampleContracts;
                    saveAllData();
                } else {
                    contractsData = localContracts ? JSON.parse(localContracts) : [];
                    let parsedSuppliers = localSuppliers ? JSON.parse(localSuppliers) : [];
                    suppliersData = parsedSuppliers.map(s => ({ ...s, pdfDataUrl: s.pdfDataUrl || null, pdfFilename: s.pdfFilename || null }));
                }
                refreshUI();
            }

            function saveAllData() {
                localStorage.setItem('marketContractsData', JSON.stringify(contractsData));
                localStorage.setItem('marketSuppliersData', JSON.stringify(suppliersData));
                refreshUI();
            }

            function saveContract(contract) {
                const existingIndex = contractsData.findIndex(c => c.id === contract.id);
                if (existingIndex > -1) {
                    contractsData[existingIndex] = contract;
                } else {
                    contract.id = contract.id || crypto.randomUUID();
                    contractsData.push(contract);
                }
                saveAllData();
            }

            function deleteContract(contractId) {
                contractsData = contractsData.filter(c => c.id !== contractId);
                saveAllData();
            }

            function addSupplier(code, name, shouldSave = true) {
                if (!code || !name) return false;
                const existing = suppliersData.find(s => s.code === code || s.name === name);
                if (existing) {
                    console.warn(`Supplier with code ${code} or name ${name} already exists.`);
                    return false;
                }
                suppliersData.push({ code, name, pdfDataUrl: null, pdfFilename: null });
                if (shouldSave) {
                    saveAllData();
                }
                return true;
            }

            function editSupplier(code, newName, pdfData) {
                const supplierIndex = suppliersData.findIndex(s => s.code === code);
                if (supplierIndex === -1) return;

                suppliersData[supplierIndex].name = newName;
                if (pdfData.dataUrl) {
                    suppliersData[supplierIndex].pdfDataUrl = pdfData.dataUrl;
                    suppliersData[supplierIndex].pdfFilename = pdfData.name;
                }
                saveAllData();
            }

            function deleteSupplier(code) {
                const supplierToDelete = suppliersData.find(s => s.code === code);
                if (!supplierToDelete) return;

                const hasContracts = contractsData.some(c => c.supplierCode === code);
                if (hasContracts) {
                    alert('لا يمكن حذف المورد لارتباطه بعقود حالية.');
                    return;
                }
                suppliersData = suppliersData.filter(s => s.code !== code);
                supplierDetailsSection.classList.add('hidden');
                saveAllData();
            }


            // --- UI REFRESH ---
            function refreshUI() {
                renderAllDashboards();
                updateFinancialSummary();
                displayReports();
                renderSuppliersList();
                populateSupplierDropdown();
                renderContractsList();
                renderDashboardSummary();
            }

            function renderDashboardSummary() {
                document.getElementById('dashboard-suppliers-count').textContent = suppliersData.length;
                document.getElementById('dashboard-contracts-count').textContent = contractsData.length;

                // حساب الإيراد الشهري المتوقع والدعم والطبليات
                let monthlyRevenue = 0;
                let totalSupport = 0;
                let rentedPallets = 0;
                const today = new Date();

                contractsData.forEach(c => {
                    if (!c.startDate || !c.endDate) return;
                    const startDate = new Date(c.startDate);
                    const endDate = new Date(c.endDate);
                    if (today >= startDate && today <= endDate) {
                        const rent = parseFloat(c.monthlyRent) || 0;
                        const supportDiscount = parseFloat(c.supportDiscount) || 0;
                        const discountMarket = parseFloat(c.discount_market) || 0;
                        const discountBranches = parseFloat(c.discount_branches) || 0;
                        const freeMarket = parseFloat(c.free_market) || 0;
                        const freeBranches = parseFloat(c.free_branches) || 0;

                        // حساب الإيراد الصافي
                        monthlyRevenue += rent * (1 - Math.min(supportDiscount, 100) / 100);

                        // حساب إجمالي الدعم
                        const supportAmount = (rent * supportDiscount / 100) +
                            (rent * discountMarket / 100) +
                            (rent * discountBranches / 100) +
                            (rent * freeMarket / 100) +
                            (rent * freeBranches / 100);
                        totalSupport += supportAmount;

                        // عد الطبليات المؤجرة
                        if (c.type === 'pallet') {
                            rentedPallets++;
                        }
                    }
                });

                document.getElementById('dashboard-monthly-revenue').textContent = monthlyRevenue.toFixed(2) + ' دينار';
                // إشعارات
                const notifications = [];
                const expiringContracts = contractsData.filter(c => {
                    if (!c.endDate) return false;
                    const endDate = new Date(c.endDate);
                    return (endDate - today) / (1000 * 60 * 60 * 24) <= 30 && (endDate - today) > 0;
                });
                if (expiringContracts.length > 0) {
                    notifications.push(`<div class=\"bg-yellow-900/70 border border-yellow-700 rounded-lg p-4 mb-2 text-yellow-200\"><i class=\"fa fa-exclamation-triangle mr-2\"></i> يوجد <b>${expiringContracts.length}</b> عقد ينتهي خلال 30 يوم.</div>`);
                }
                const newSuppliers = suppliersData.filter(s => s.isNew);
                if (newSuppliers.length > 0) {
                    notifications.push(`<div class=\"bg-blue-900/70 border border-blue-700 rounded-lg p-4 mb-2 text-blue-200\"><i class=\"fa fa-user-plus mr-2\"></i> تم إضافة <b>${newSuppliers.length}</b> مورد جديد مؤخراً.</div>`);
                }
                document.getElementById('dashboard-notifications').innerHTML = notifications.join('');
            }

            // --- UI RENDERING ---
            function renderAllDashboards() {
                renderConsumablesDashboard();
                renderSpicesDashboard();
            }

            function renderConsumablesDashboard() {
                palletsDashboard.innerHTML = '';
                for (let p = 1; p <= RENTAL_CONFIG.consumable.pallets; p++) {
                    const palletId = `استهلاكي-${p}`;
                    const palletDiv = document.createElement('div');
                    palletDiv.className = 'bg-slate-900 p-4 rounded-lg shadow-md border border-slate-800';
                    let slotsHTML = '';
                    for (let s = 1; s <= 4; s++) {
                        const contract = contractsData.find(c => c.type === 'pallet' && c.unitId === palletId && c.slot === s);
                        const status = getUnitStatus(contract);
                        slotsHTML += `
                        <div class="slot unit-selector border border-slate-700 rounded-md p-3 text-center cursor-pointer ${status.bgColor} hover:ring-2 hover:ring-blue-500" 
                             data-unit-type="pallet" data-unit-id="${palletId}" data-slot-id="${s}">
                            <div class="font-bold text-lg text-white">عين ${s}</div>
                            <div class="text-sm font-semibold">${status.text}</div>
                            <div class="text-xs text-slate-400 mt-1">${contract ? `(${contract.supplierCode})` : 'شاغر'}</div>
                        </div>`;
                    }
                    palletDiv.innerHTML = `<h3 class="font-bold text-lg mb-3 text-center border-b border-slate-700 pb-2 text-white">طبلية ${p}</h3><div class="grid grid-cols-2 gap-2">${slotsHTML}</div>`;
                    palletsDashboard.appendChild(palletDiv);
                }
                addClickListenersToUnits();
            }

            function renderSpicesDashboard() {
                spicesDashboard.innerHTML = '';
                // Render Stands
                for (let i = 1; i <= RENTAL_CONFIG.spice.stands; i++) {
                    const unitId = `استاند-${i}`;
                    const contract = contractsData.find(c => c.type === 'stand' && c.unitId === unitId);
                    const status = getUnitStatus(contract);
                    const unitHTML = `
                    <div class="unit unit-selector bg-slate-900 border border-slate-700 rounded-md p-3 text-center cursor-pointer ${status.bgColor} hover:ring-2 hover:ring-blue-500" data-unit-type="stand" data-unit-id="${unitId}">
                        <div class="font-bold text-lg text-white">استاند ${i}</div>
                        <div class="text-sm font-semibold">${status.text}</div>
                        ${contract && contract.length ? `<div class="text-xs mt-1">(${contract.length} متر)</div>` : ''}
                    </div>`;
                    spicesDashboard.innerHTML += unitHTML;
                }
                // Render Gondolas
                for (let i = 1; i <= RENTAL_CONFIG.spice.gondolas; i++) {
                    const unitId = `جندوله-${i}`;
                    const contract = contractsData.find(c => c.type === 'gondola' && c.unitId === unitId);
                    const status = getUnitStatus(contract);
                    const unitHTML = `
                    <div class="unit unit-selector bg-slate-900 border border-slate-700 rounded-md p-3 text-center cursor-pointer ${status.bgColor} hover:ring-2 hover:ring-blue-500" data-unit-type="gondola" data-unit-id="${unitId}">
                        <div class="font-bold text-lg text-white">جندوله ${i}</div>
                        <div class="text-sm font-semibold">${status.text}</div>
                    </div>`;
                    spicesDashboard.innerHTML += unitHTML;
                }
                addClickListenersToUnits();
            }

            function renderSuppliersList() {
                suppliersListContainer.innerHTML = '';
                if (suppliersData.length === 0) {
                    suppliersListContainer.innerHTML = '<p class="text-slate-400">لا يوجد موردين مسجلين.</p>';
                    return;
                }
                const selectedCode = editSupplierCodeInput.value;
                suppliersData.forEach(supplier => {
                    const div = document.createElement('div');
                    div.className = `list-item cursor-pointer p-3 bg-slate-800 rounded-md hover:bg-slate-700 transition-colors border-2 border-transparent`;
                    if (supplier.code === selectedCode) {
                        div.classList.add('selected');
                    }
                    div.dataset.code = supplier.code;
                    div.innerHTML = `
                    <span class="text-slate-200">
                        <strong class="font-mono text-blue-400">${supplier.code}</strong> - ${supplier.name}
                    </span>
                `;
                    div.addEventListener('click', () => showSupplierDetails(supplier.code));
                    suppliersListContainer.appendChild(div);
                });
            }

            function populateSupplierDropdown() {
                supplierSelect.innerHTML = '<option value="">-- اختر مورداً --</option>';
                suppliersData.forEach(s => {
                    supplierSelect.innerHTML += `<option value="${s.code}">${s.code} - ${s.name}</option>`;
                });
            }

            function getUnitStatus(contract) {
                if (!contract) return { bgColor: 'bg-slate-700', text: 'شاغر' };
                const supplier = suppliersData.find(s => s.code === contract.supplierCode);
                const supplierName = supplier ? supplier.name : 'مورد غير معروف';

                const today = new Date(); today.setHours(0, 0, 0, 0);
                if (!contract.startDate || !contract.endDate) return { bgColor: 'bg-slate-700', text: 'بيانات خاطئة' };
                const startDate = new Date(contract.startDate); const endDate = new Date(contract.endDate);
                if (today < startDate || today > endDate) return { bgColor: 'bg-slate-700', text: 'شاغر (عقد منتهي)' };

                if (parseFloat(contract.free_market || 0) > 0 || parseFloat(contract.free_branches || 0) > 0) {
                    return { bgColor: 'bg-yellow-500 text-black', text: supplierName };
                }
                if (contract.paymentStatus === 'due') return { bgColor: 'bg-red-600 text-white', text: supplierName };
                return { bgColor: 'bg-green-600 text-white', text: supplierName };
            }

            function renderContractsList() {
                const searchTerm = contractsSearchInput.value.toLowerCase();
                const filteredContracts = contractsData.filter(c => {
                    const supplier = suppliersData.find(s => s.code === c.supplierCode);
                    const supplierName = supplier ? supplier.name.toLowerCase() : '';
                    const supplierCode = c.supplierCode ? c.supplierCode.toLowerCase() : '';
                    const unitId = c.unitId ? c.unitId.toLowerCase() : '';
                    return supplierName.includes(searchTerm) || supplierCode.includes(searchTerm) || unitId.includes(searchTerm);
                });

                contractsList.innerHTML = '';
                if (filteredContracts.length === 0) {
                    contractsList.innerHTML = '<p class="text-slate-400 text-center p-4">لا توجد عقود تطابق البحث.</p>';
                    return;
                }

                filteredContracts.forEach(contract => {
                    const supplier = suppliersData.find(s => s.code === contract.supplierCode);
                    const isUnassigned = !contract.type;
                    const unitName = isUnassigned
                        ? `<span class="text-yellow-400">(غير معين لوحدة)</span>`
                        : (contract.type === 'pallet' ? `${contract.unitId} / عين ${contract.slot}` : contract.unitId);

                    const div = document.createElement('div');
                    div.className = `p-4 rounded-lg cursor-pointer transition-colors ${isUnassigned ? 'bg-yellow-900/50 hover:bg-yellow-900/70' : 'bg-slate-800 hover:bg-slate-700'}`;
                    div.innerHTML = `
                    <div class="flex justify-between items-start">
                        <div>
                            <p class="font-bold text-lg text-white">${supplier ? supplier.name : 'مورد غير معروف'} <span class="text-sm text-blue-400 font-mono">(${contract.supplierCode})</span></p>
                            <p class="text-slate-300">${unitName}</p>
                        </div>
                        <div class="text-left">
                            <p class="text-sm text-slate-400">يبدأ: ${contract.startDate || 'N/A'}</p>
                            <p class="text-sm text-slate-400">ينتهي: ${contract.endDate || 'N/A'}</p>
                        </div>
                    </div>
                `;
                    div.onclick = () => showEditContractModal(contract);
                    contractsList.appendChild(div);
                });
            }

            // --- FINANCIAL & REPORTING CALCULATIONS ---
            function updateFinancialSummary() {
                let monthlyRevenue = 0;
                let totalSupport = 0;
                let occupiedUnits = 0;
                let totalUnits = 0;
                let rentedPallets = 0;
                const today = new Date();

                const activeContracts = contractsData.filter(c => {
                    if (!c.type || !c.startDate || !c.endDate) return false;
                    const startDate = new Date(c.startDate); const endDate = new Date(c.endDate);
                    return today >= startDate && today <= endDate;
                });

                activeContracts.forEach(c => {
                    const rent = parseFloat(c.monthlyRent) || 0;
                    const supportDiscount = parseFloat(c.supportDiscount) || 0;
                    const discountMarket = parseFloat(c.discount_market) || 0;
                    const discountBranches = parseFloat(c.discount_branches) || 0;
                    const freeMarket = parseFloat(c.free_market) || 0;
                    const freeBranches = parseFloat(c.free_branches) || 0;

                    // حساب الإيراد بعد الخصومات
                    const netRent = rent * (1 - Math.min(supportDiscount, 100) / 100);
                    monthlyRevenue += netRent;

                    // حساب إجمالي الدعم (الخصومات والمجاني)
                    const supportAmount = (rent * supportDiscount / 100) +
                        (rent * discountMarket / 100) +
                        (rent * discountBranches / 100) +
                        (rent * freeMarket / 100) +
                        (rent * freeBranches / 100);
                    totalSupport += supportAmount;

                    // عد الطبليات المؤجرة
                    if (c.type === 'pallet') {
                        rentedPallets++;
                    }
                });

                occupiedUnits = activeContracts.length;
                totalUnits = (RENTAL_CONFIG.consumable.pallets * 4) + RENTAL_CONFIG.spice.stands + RENTAL_CONFIG.spice.gondolas;
                const totalPalletSlots = RENTAL_CONFIG.consumable.pallets * 4; // 15 طبلية × 4 عيون = 60 عين
                const occupancyRate = totalUnits > 0 ? (occupiedUnits / totalUnits) * 100 : 0;

                // تحديث العرض
                document.getElementById('monthly-revenue').textContent = `${monthlyRevenue.toFixed(2)} دينار`;
                document.getElementById('annual-revenue').textContent = `${(monthlyRevenue * 12).toFixed(2)} دينار`;
                document.getElementById('total-support').textContent = `${totalSupport.toFixed(2)} دينار`;
                document.getElementById('rented-pallets').textContent = `${rentedPallets} / ${totalPalletSlots}`;
                document.getElementById('occupancy-rate').textContent = `${occupancyRate.toFixed(1)}%`;
            }

            function displayReports() {
                const today = new Date();
                const thirtyDaysFromNow = new Date();
                thirtyDaysFromNow.setDate(today.getDate() + 30);

                const activeContracts = contractsData.filter(c => {
                    if (!c.type || !c.startDate || !c.endDate) return false;
                    const startDate = new Date(c.startDate); const endDate = new Date(c.endDate);
                    return today >= startDate && today <= endDate;
                });

                // حساب تفاصيل الدعم والطبليات
                let totalSupport = 0;
                let governmentSupport = 0;
                let marketDiscount = 0;
                let branchesDiscount = 0;
                let marketFree = 0;
                let branchesFree = 0;
                let rentedPallets = 0;

                activeContracts.forEach(c => {
                    const rent = parseFloat(c.monthlyRent) || 0;
                    const supportDiscount = parseFloat(c.supportDiscount) || 0;
                    const discountMarket = parseFloat(c.discount_market) || 0;
                    const discountBranches = parseFloat(c.discount_branches) || 0;
                    const freeMarket = parseFloat(c.free_market) || 0;
                    const freeBranches = parseFloat(c.free_branches) || 0;

                    // حساب كل نوع دعم
                    governmentSupport += rent * supportDiscount / 100;
                    marketDiscount += rent * discountMarket / 100;
                    branchesDiscount += rent * discountBranches / 100;
                    marketFree += rent * freeMarket / 100;
                    branchesFree += rent * freeBranches / 100;

                    totalSupport += governmentSupport + marketDiscount + branchesDiscount + marketFree + branchesFree;

                    // عد الطبليات المؤجرة
                    if (c.type === 'pallet') {
                        rentedPallets++;
                    }
                });

                const totalPalletSlots = RENTAL_CONFIG.consumable.pallets * 4; // 15 × 4 = 60
                const palletOccupancy = totalPalletSlots > 0 ? (rentedPallets / totalPalletSlots) * 100 : 0;

                // تحديث عناصر التقرير
                document.getElementById('report-total-support').textContent = `${totalSupport.toFixed(2)} دينار`;
                document.getElementById('report-rented-pallets').textContent = `${rentedPallets} / ${totalPalletSlots}`;
                document.getElementById('report-pallet-occupancy').textContent = `${palletOccupancy.toFixed(1)}%`;

                // تحديث تفاصيل أنواع الدعم
                document.getElementById('support-government').textContent = governmentSupport.toFixed(2);
                document.getElementById('support-market-discount').textContent = marketDiscount.toFixed(2);
                document.getElementById('support-branches-discount').textContent = branchesDiscount.toFixed(2);
                document.getElementById('support-market-free').textContent = marketFree.toFixed(2);
                document.getElementById('support-branches-free').textContent = branchesFree.toFixed(2);

                const expiring = activeContracts.filter(c => {
                    if (!c.endDate) return false;
                    const endDate = new Date(c.endDate);
                    return endDate <= thirtyDaysFromNow;
                });

                const due = activeContracts.filter(c => c.paymentStatus === 'due');

                renderReportList(expiringContractsContainer, expiring, (c) => {
                    const daysLeft = Math.ceil((new Date(c.endDate) - today) / (1000 * 60 * 60 * 24));
                    return `ينتهي خلال ${daysLeft} يوم`;
                }, 'yellow');

                renderReportList(dueContractsContainer, due, () => 'مستحق الدفع', 'red');
            }

            function renderReportList(container, contracts, getStatusText, color) {
                container.innerHTML = '';
                if (contracts.length === 0) {
                    container.innerHTML = `<p class="text-slate-400">لا توجد عقود تنطبق عليها هذه الحالة.</p>`;
                    return;
                }
                contracts.sort((a, b) => new Date(a.endDate) - new Date(b.endDate));
                contracts.forEach(contract => {
                    const supplier = suppliersData.find(s => s.code === contract.supplierCode);
                    const resultEl = document.createElement('div');
                    resultEl.className = `p-2 bg-${color}-900/50 border border-${color}-700 rounded-md cursor-pointer hover:bg-${color}-900/70 text-${color}-200`;
                    resultEl.innerHTML = `<strong class="text-${color}-100">${supplier ? supplier.name : contract.supplierCode}</strong> - <span>${getStatusText(contract)}</span>`;
                    resultEl.onclick = () => showEditContractModal(contract);
                    container.appendChild(resultEl);
                });
            }

            // --- NAVIGATION LOGIC ---
            function showPage(pageId) {
                pages.forEach(page => page.classList.add('hidden'));
                document.getElementById(pageId).classList.remove('hidden');

                navLinks.forEach(link => {
                    link.classList.toggle('active', link.dataset.target === pageId);
                });
                if (pageId !== 'page-suppliers') {
                    supplierDetailsSection.classList.add('hidden');
                }
            }


            // --- EVENT HANDLERS & MODAL LOGIC ---
            function setupEventListeners() {
                addContractBtn.addEventListener('click', showAddContractModal);
                closeModalBtn.addEventListener('click', hideModal);
                modal.addEventListener('click', (e) => { if (e.target === modal) hideModal(); });
                contractForm.addEventListener('submit', handleFormSubmit);
                deleteContractBtn.addEventListener('click', () => {
                    const contractId = document.getElementById('contract-id').value;
                    if (contractId) {
                        confirmAction('هل أنت متأكد من حذف هذا العقد؟', () => {
                            deleteContract(contractId);
                            hideModal();
                        });
                    }
                });
                supplierForm.addEventListener('submit', (e) => {
                    e.preventDefault();
                    addSupplier(supplierCodeInput.value.trim(), supplierNameInput.value.trim());
                    supplierForm.reset();
                });
                sectionTypeSelect.addEventListener('change', updateModalForm);
                spiceUnitTypeSelect.addEventListener('change', updateModalForm);
                standLengthInput.addEventListener('input', updatePrice);
                contractsSearchInput.addEventListener('input', renderContractsList);
                importFilesBtn.addEventListener('click', () => fileInput.click());
                fileInput.addEventListener('change', handleFileImport);
                printReportsBtn.addEventListener('click', printReports);

                navLinks.forEach(link => {
                    link.addEventListener('click', (e) => {
                        e.preventDefault();
                        showPage(link.dataset.target)
                    });
                });

                // Supplier Details Form Handlers
                editSupplierForm.addEventListener('submit', handleEditSupplierSubmit);
                deleteSupplierBtn.addEventListener('click', () => {
                    const code = editSupplierCodeInput.value;
                    if (code) {
                        confirmAction(`هل أنت متأكد من حذف المورد صاحب الكود ${code}?`, () => deleteSupplier(code));
                    }
                });
                supplierPdfInput.addEventListener('change', handleSupplierPdfChange);
            }

            function handleFileImport(event) {
                const file = event.target.files[0];
                if (!file) return;

                const reader = new FileReader();

                if (file.type === 'application/json') {
                    reader.onload = (e) => {
                        try {
                            const sanitizedJsonString = e.target.result.replace(/\bNaN\b/g, 'null');
                            const json = JSON.parse(sanitizedJsonString);
                            processContractImport(json);
                        } catch (error) {
                            console.error("Error parsing JSON file:", error);
                            alert("حدث خطأ أثناء معالجة ملف JSON. يرجى التأكد من أن الملف صالح.");
                        }
                    };
                    reader.readAsText(file, 'UTF-8');
                } else { // Handle Excel/CSV
                    reader.onload = (e) => {
                        try {
                            const data = new Uint8Array(e.target.result);
                            const workbook = XLSX.read(data, { type: 'array' });
                            const firstSheetName = workbook.SheetNames[0];
                            const worksheet = workbook.Sheets[firstSheetName];
                            const json = XLSX.utils.sheet_to_json(worksheet, { header: true });
                            const headers = XLSX.utils.sheet_to_json(worksheet, { header: 1 })[0];

                            if (headers.includes('المورد') && (headers.includes('بداية العقد') || headers.includes('StartDate'))) {
                                processContractImport(json);
                            } else {
                                processSupplierImport(XLSX.utils.sheet_to_json(worksheet, { header: 1 }));
                            }
                        } catch (error) {
                            console.error("Error processing Excel/CSV file:", error);
                            alert("حدث خطأ أثناء معالجة الملف. يرجى التأكد من أنه ملف Excel أو CSV صالح.");
                        }
                    };
                    reader.readAsArrayBuffer(file);
                }
                event.target.value = '';
            }

            function processSupplierImport(json) {
                let addedCount = 0;
                let maxCode = suppliersData.map(s => parseInt(s.code, 10)).filter(n => !isNaN(n)).reduce((max, n) => Math.max(max, n), 1000);

                for (let i = 0; i < json.length; i++) {
                    const row = json[i];
                    if (!row || row.length === 0 || !row[0]) continue;
                    let code, name;
                    if (row.length >= 2 && row[0] && row[1]) {
                        code = String(row[0]).trim();
                        name = String(row[1]).trim();
                    } else {
                        name = String(row[0]).trim();
                        maxCode++;
                        code = String(maxCode);
                    }
                    if (code && name && addSupplier(code, name, false)) {
                        addedCount++;
                    }
                }
                if (addedCount > 0) {
                    saveAllData();
                    alert(`تم استيراد ودمج ${addedCount} مورد بنجاح.`);
                } else {
                    alert('لم يتم العثور على موردين جدد في الملف أو أن الموردين موجودون بالفعل.');
                }
            }

            function excelDateToJSDate(serial) {
                if (!serial || isNaN(serial)) return null;
                const utc_days = Math.floor(serial - 25569);
                const utc_value = utc_days * 86400;
                const date_info = new Date(utc_value * 1000);
                const fractional_day = serial - Math.floor(serial) + 0.0000001;
                let total_seconds = Math.floor(86400 * fractional_day);
                const seconds = total_seconds % 60;
                total_seconds -= seconds;
                const hours = Math.floor(total_seconds / (60 * 60));
                const minutes = Math.floor(total_seconds / 60) % 60;
                const d = new Date(date_info.getFullYear(), date_info.getMonth(), date_info.getDate(), hours, minutes, seconds);
                return d.toISOString().split('T')[0];
            }

            function parseDate(dateInput) {
                if (!dateInput || typeof dateInput === 'string' && (dateInput.toLowerCase() === 'nan' || dateInput.includes('لا يوجد'))) {
                    return null;
                }
                if (!isNaN(dateInput) && typeof dateInput === 'number') {
                    return excelDateToJSDate(dateInput);
                }
                const dateStr = String(dateInput).split(' ')[0];
                const parts = dateStr.split(/[\/\-]/);
                if (parts.length === 3) {
                    if (parts[0].length === 4) return `${parts[0]}-${parts[1].padStart(2, '0')}-${parts[2].padStart(2, '0')}`;
                    return `${parts[2]}-${parts[1].padStart(2, '0')}-${parts[0].padStart(2, '0')}`;
                }
                if (parts.length === 2) {
                    return `${new Date().getFullYear()}-${parts[1].padStart(2, '0')}-${parts[0].padStart(2, '0')}`;
                }
                return null;
            }

            function processContractImport(json) {
                let addedContracts = 0;
                let addedSuppliers = 0;
                let maxCode = suppliersData.map(s => parseInt(s.code, 10)).filter(n => !isNaN(n)).reduce((max, n) => Math.max(max, n), 1000);

                json.forEach(row => {
                    const supplierName = row['Supplier'] || row['المورد'] ? String(row['Supplier'] || row['المورد']).trim() : null;
                    if (!supplierName) return;

                    let supplier = suppliersData.find(s => s.name === supplierName);
                    if (!supplier) {
                        maxCode++;
                        const newCode = String(maxCode);
                        addSupplier(newCode, supplierName, false);
                        supplier = { code: newCode, name: supplierName };
                        addedSuppliers++;
                    }

                    const notes = [
                        row['Location'] || row['الموقع'],
                        row['AgreementDetails'] || row['الاتفاق'],
                        row['Area'] || row['المساحــات']
                    ].filter(Boolean).join('\n');

                    const newContract = {
                        id: crypto.randomUUID(),
                        supplierCode: supplier.code,
                        startDate: parseDate(row['StartDate'] || row['بداية العقد']),
                        endDate: parseDate(row['EndDate'] || row['نهاية العقد']),
                        notes: notes,
                        type: null,
                        paymentStatus: 'due',
                    };

                    const isDuplicate = contractsData.some(c =>
                        c.supplierCode === newContract.supplierCode &&
                        c.startDate === newContract.startDate &&
                        c.endDate === newContract.endDate
                    );

                    if (!isDuplicate) {
                        contractsData.push(newContract);
                        addedContracts++;
                    }
                });

                if (addedContracts > 0) {
                    saveAllData();
                    alert(`تم استيراد ${addedContracts} عقد جديد و ${addedSuppliers} مورد جديد بنجاح.`);
                } else {
                    alert('لم يتم العثور على عقود جديدة في الملف أو أنها مكررة.');
                }
            }

            function printReports() {
                alert("سيتم تنفيذ وظيفة الطباعة في التحديثات القادمة.");
            }

            function addClickListenersToUnits() {
                document.querySelectorAll('.unit-selector').forEach(unitEl => {
                    unitEl.addEventListener('click', handleUnitClick);
                });
            }

            function handleUnitClick(event) {
                const el = event.currentTarget;
                const unitType = el.dataset.unitType;
                const unitId = el.dataset.unitId;
                const slotId = el.dataset.slotId;

                let contract;
                if (unitType === 'pallet') {
                    contract = contractsData.find(c => c.type === 'pallet' && c.unitId === unitId && c.slot == slotId);
                } else {
                    contract = contractsData.find(c => c.type === unitType && c.unitId === unitId);
                }

                if (contract) showEditContractModal(contract);
                else showAddContractModal({ unitType, unitId, slotId });
            }

            function handleFormSubmit(e) {
                e.preventDefault();

                const contractId = document.getElementById('contract-id').value;
                let contract = contractsData.find(c => c.id === contractId) || {};

                // Update all fields
                contract.id = contractId || crypto.randomUUID();
                contract.supplierCode = supplierSelect.value;
                contract.startDate = document.getElementById('start-date').value;
                contract.endDate = document.getElementById('end-date').value;
                contract.monthlyRent = monthlyRentInput.value;
                contract.supportDiscount = document.getElementById('support-discount').value;
                contract.discount_market = document.getElementById('discount_market').value;
                contract.discount_branches = document.getElementById('discount_branches').value;
                contract.free_market = document.getElementById('free_market').value;
                contract.free_branches = document.getElementById('free_branches').value;
                contract.collectionMethod = document.getElementById('collection-method').value;
                contract.paymentStatus = document.querySelector('input[name="paymentStatus"]:checked').value;
                contract.notes = contractNotesTextarea.value;

                const section = sectionTypeSelect.value;
                if (section === 'consumable') {
                    contract.type = 'pallet';
                    contract.unitId = document.getElementById('pallet-number').value;
                    contract.slot = parseInt(document.getElementById('slot-number').value);
                } else {
                    const spiceType = spiceUnitTypeSelect.value;
                    contract.type = spiceType;
                    if (spiceType === 'stand') {
                        contract.unitId = document.getElementById('stand-id').value;
                        contract.length = parseFloat(document.getElementById('stand-length').value);
                    } else {
                        contract.unitId = document.getElementById('gondola-id').value;
                        contract.length = null;
                    }
                }

                if (!contract.supplierCode || !contract.startDate || !contract.endDate) {
                    alert('يرجى اختيار مورد وتعبئة تواريخ الإيجار.');
                    return;
                }
                saveContract(contract);
                hideModal();
            }

            function confirmAction(message, callback) {
                const confirmModal = document.getElementById('confirm-modal');
                const confirmText = document.getElementById('confirm-text');
                const confirmOkBtn = document.getElementById('confirm-ok-btn');
                const confirmCancelBtn = document.getElementById('confirm-cancel-btn');

                confirmText.textContent = message;
                confirmModal.classList.remove('hidden');

                const resolution = new Promise(resolve => {
                    confirmOkBtn.onclick = () => resolve(true);
                    confirmCancelBtn.onclick = () => resolve(false);
                    confirmModal.onclick = (e) => { if (e.target === confirmModal) resolve(false); };
                });

                resolution.then(confirmed => {
                    confirmModal.classList.add('hidden');
                    if (confirmed) {
                        callback();
                    }
                });
            }

            function updateModalForm() {
                const section = sectionTypeSelect.value;
                const spiceType = spiceUnitTypeSelect.value;

                document.getElementById('consumable-fields').classList.toggle('hidden', section !== 'consumable');
                document.getElementById('spice-fields').classList.toggle('hidden', section !== 'spice');

                if (section === 'spice') {
                    document.getElementById('stand-fields').classList.toggle('hidden', spiceType !== 'stand');
                    document.getElementById('gondola-fields').classList.toggle('hidden', spiceType !== 'gondola');
                }
                updatePrice();
            }

            function updatePrice() {
                const section = sectionTypeSelect.value;
                let price = 0;
                if (section === 'consumable') {
                    price = RENTAL_CONFIG.prices.palletSlot;
                } else {
                    const spiceType = spiceUnitTypeSelect.value;
                    if (spiceType === 'stand') {
                        const length = parseFloat(standLengthInput.value) || 0;
                        price = length * RENTAL_CONFIG.prices.standPerMeter;
                    } else {
                        price = RENTAL_CONFIG.prices.gondola;
                    }
                }
                monthlyRentInput.value = price.toFixed(2);
            }

            function showAddContractModal(prefill = {}) {
                contractForm.reset();
                modalTitle.textContent = "إضافة عقد جديد";
                deleteContractBtn.classList.add('hidden');
                document.getElementById('support-discount').value = '0';
                document.getElementById('discount_market').value = '0';
                document.getElementById('discount_branches').value = '0';
                document.getElementById('free_market').value = '0';
                document.getElementById('free_branches').value = '0';
                unitSelectionFieldset.classList.remove('hidden');
                notesFieldset.classList.remove('hidden');
                contractNotesTextarea.value = '';

                if (prefill.unitType) {
                    const section = (prefill.unitType === 'pallet') ? 'consumable' : 'spice';
                    sectionTypeSelect.value = section;
                    if (section === 'consumable') {
                        document.getElementById('pallet-number').value = prefill.unitId;
                        document.getElementById('slot-number').value = prefill.slotId;
                    } else {
                        spiceUnitTypeSelect.value = prefill.unitType;
                        document.getElementById(`${prefill.unitType}-id`).value = prefill.unitId;
                    }
                }

                updateModalForm();
                showModal();
            }

            function showEditContractModal(contract) {
                contractForm.reset();
                modalTitle.textContent = "تعديل العقد";
                deleteContractBtn.classList.remove('hidden');

                document.getElementById('contract-id').value = contract.id;

                notesFieldset.classList.remove('hidden');
                contractNotesTextarea.value = contract.notes || '';

                if (contract.type) {
                    unitSelectionFieldset.classList.remove('hidden');
                    const section = (contract.type === 'pallet') ? 'consumable' : 'spice';
                    sectionTypeSelect.value = section;

                    if (section === 'consumable') {
                        document.getElementById('pallet-number').value = contract.unitId;
                        document.getElementById('slot-number').value = contract.slot;
                    } else {
                        spiceUnitTypeSelect.value = contract.type;
                        document.getElementById(`${contract.type}-id`).value = contract.unitId;
                        if (contract.type === 'stand') {
                            document.getElementById('stand-length').value = contract.length;
                        }
                    }
                } else {
                    unitSelectionFieldset.classList.remove('hidden');
                    modalTitle.textContent = "تعيين عقد مستورد";
                }

                supplierSelect.value = contract.supplierCode;
                document.getElementById('start-date').value = contract.startDate;
                document.getElementById('end-date').value = contract.endDate;
                monthlyRentInput.value = contract.monthlyRent || 0;
                document.getElementById('support-discount').value = contract.supportDiscount || 0;
                document.getElementById('discount_market').value = contract.discount_market || 0;
                document.getElementById('discount_branches').value = contract.discount_branches || 0;
                document.getElementById('free_market').value = contract.free_market || 0;
                document.getElementById('free_branches').value = contract.free_branches || 0;
                document.getElementById('collection-method').value = contract.collectionMethod || 'نقدي';
                document.querySelector(`input[name="paymentStatus"][value="${contract.paymentStatus || 'due'}"]`).checked = true;

                updateModalForm();
                showModal();
            }

            // --- Supplier Details Logic ---
            function showSupplierDetails(code) {
                const supplier = suppliersData.find(s => s.code === code);
                if (!supplier) return;
                tempSupplierPdf = { dataUrl: null, name: null };
                supplierDetailsSection.classList.remove('hidden');
                editSupplierCodeInput.value = supplier.code;
                editSupplierNameInput.value = supplier.name;
                renderSupplierPdfDisplay(supplier);
                renderSuppliersList();
            }

            function renderSupplierPdfDisplay(supplier) {
                supplierPdfDisplay.innerHTML = '';
                if (supplier && supplier.pdfDataUrl && supplier.pdfFilename) {
                    const pdfLink = document.createElement('a');
                    pdfLink.href = supplier.pdfDataUrl;
                    pdfLink.target = '_blank';
                    pdfLink.textContent = `عرض الملف الحالي: ${supplier.pdfFilename}`;
                    pdfLink.className = "text-blue-400 hover:underline";

                    const removeBtn = document.createElement('button');
                    removeBtn.textContent = 'X';
                    removeBtn.type = 'button';
                    removeBtn.className = 'mr-2 text-red-500 font-bold';
                    removeBtn.onclick = () => {
                        confirmAction('هل أنت متأكد من حذف ملف PDF الخاص بهذا المورد؟', () => {
                            const supplierIndex = suppliersData.findIndex(s => s.code === supplier.code);
                            if (supplierIndex > -1) {
                                suppliersData[supplierIndex].pdfDataUrl = null;
                                suppliersData[supplierIndex].pdfFilename = null;
                                saveAllData();
                                showSupplierDetails(supplier.code);
                            }
                        });
                    };

                    supplierPdfDisplay.appendChild(removeBtn);
                    supplierPdfDisplay.appendChild(pdfLink);
                } else {
                    supplierPdfDisplay.textContent = 'لا يوجد ملف مرفق حالياً.';
                }
            }

            function handleSupplierPdfChange(event) {
                const file = event.target.files[0];
                if (!file) return;
                if (file.size > 5 * 1024 * 1024) {
                    alert("حجم الملف كبير جداً. يرجى اختيار ملف أصغر من 5 ميجابايت.");
                    supplierPdfInput.value = '';
                    return;
                }
                const reader = new FileReader();
                reader.onload = (e) => {
                    tempSupplierPdf = { dataUrl: e.target.result, name: file.name };
                    supplierPdfDisplay.innerHTML = `<span class="text-green-400">تم اختيار الملف: ${file.name} (سيتم الحفظ بعد الضغط على حفظ التغييرات)</span>`;
                };
                reader.readAsDataURL(file);
            }

            function handleEditSupplierSubmit(e) {
                e.preventDefault();
                const code = editSupplierCodeInput.value;
                const newName = editSupplierNameInput.value.trim();
                if (!code || !newName) {
                    alert('اسم المورد لا يمكن أن يكون فارغاً.');
                    return;
                }
                editSupplier(code, newName, tempSupplierPdf);
                showSupplierDetails(code);
            }

            function populateFormSelects() {
                sectionTypeSelect.innerHTML = `
                <option value="consumable">قسم الاستهلاكي (طبالي)</option>
                <option value="spice">قسم البهارات (استاندات/جندولات)</option>
            `;
                const palletSelect = document.getElementById('pallet-number');
                palletSelect.innerHTML = '';
                for (let i = 1; i <= RENTAL_CONFIG.consumable.pallets; i++) palletSelect.innerHTML += `<option value="استهلاكي-${i}">طبلية ${i}</option>`;

                const slotSelect = document.getElementById('slot-number');
                slotSelect.innerHTML = '';
                for (let i = 1; i <= 4; i++) slotSelect.innerHTML += `<option value="${i}">${i}</option>`;

                spiceUnitTypeSelect.innerHTML = `
                <option value="stand">استاند (بالمتر)</option>
                <option value="gondola">جندوله (وحدة)</option>
            `;
                const standSelect = document.getElementById('stand-id');
                standSelect.innerHTML = '';
                for (let i = 1; i <= RENTAL_CONFIG.spice.stands; i++) standSelect.innerHTML += `<option value="استاند-${i}">استاند ${i}</option>`;

                const gondolaSelect = document.getElementById('gondola-id');
                gondolaSelect.innerHTML = '';
                for (let i = 1; i <= RENTAL_CONFIG.spice.gondolas; i++) gondolaSelect.innerHTML += `<option value="جندوله-${i}">جندوله ${i}</option>`;
            }

            function showModal() {
                modal.classList.remove('hidden');
                setTimeout(() => {
                    modalContent.classList.remove('scale-95');
                    modalContent.classList.add('scale-100');
                }, 10);
            }

            function hideModal() {
                modalContent.classList.remove('scale-100');
                modalContent.classList.add('scale-95');
                setTimeout(() => modal.classList.add('hidden'), 300);
            }

            // --- START THE APP ---
            function main() {
                setupEventListeners();
                populateFormSelects();
                loadAllDataFromLocal();
                showPage('page-dashboard');
            }

            main();
        });
    </script>
</body>

</html>